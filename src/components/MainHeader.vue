<script setup lang="ts">
import { ref, onMounted, computed, watch, defineAsyncComponent } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useAuthStore } from 'src/stores/auth';
import type { Assessment, User } from 'src/types/models';
import { useGlobalStore } from 'src/stores/global';
import EvaluateDuplicateDialog from './evaluate/EvaluateDuplicateDialog.vue';
import { api } from 'src/boot/axios';
import { useAssessmentStore } from 'src/stores/asm';
import { useBlockCreatorStore } from 'src/stores/block_creator';
import { AssessmentService } from 'src/services/asm/assessmentService';
import { useQuasar } from 'quasar';
import ChangStatusDialog from 'src/components/common/ChangStatusDialog.vue';
import ConfirmDialog from 'src/components/ConfirmDialog.vue';

const asmStore = useAssessmentStore();
const authStore = useAuthStore();
const route = useRoute();
const globalStore = useGlobalStore();
const router = useRouter();
// const showEvaluateDuplicate = ref(false);
const changStatusDialog = ref<{
  openDialog: (id: number, status: boolean, buttonText: string) => void;
} | null>(null);
const blockCreatorStore = useBlockCreatorStore();
const user = ref<User>();
const editableTitle = ref<string>(''); // ตัวแปรที่ใช้กับ q-input
const isSavingTitle = ref(false); // แสดงสถานะการบันทึก
const $q = useQuasar();
const showLogoutConfirm = ref(false);

const confirmLogout = () => {
  showLogoutConfirm.value = true;
};

const handleLogoutConfirmed = () => {
  authStore.logout();
};
// Use name from assessment in store (since 'title' does not exist, use 'name')
const asmTitle = computed({
  get: () => {
    // For evaluate routes, prioritize blockCreatorStore, otherwise use asmStore
    if (isEvaluateEditor.value && blockCreatorStore.currentAssessment?.name) {
      return blockCreatorStore.currentAssessment.name;
    }
    return asmStore.currentAssessment?.name || '';
  },
  set: (val: string) => {
    // Update both stores to keep them synchronized
    if (asmStore.currentAssessment) {
      asmStore.currentAssessment.name = val;
    }
    if (isEvaluateEditor.value && blockCreatorStore.currentAssessment) {
      blockCreatorStore.currentAssessment.name = val;
    }
  },
});

// debounce สำหรับการ save ชื่อแบบฟอร์ม
const saveTitle = async (newTitle: string) => {
  try {
    isSavingTitle.value = true;
    globalStore.startSaveOperation('Saving...');
    await api.patch(`assessments/${route.params.id as string}`, {
      name: newTitle,
    });

    // Update both stores to keep them synchronized
    asmTitle.value = newTitle;

    // Update asmStore
    if (asmStore.currentAssessment) {
      asmStore.currentAssessment.name = newTitle;
    }

    // Update blockCreatorStore for evaluate routes
    if (isEvaluateEditor.value && blockCreatorStore.currentAssessment) {
      blockCreatorStore.currentAssessment.name = newTitle;
    }

    globalStore.completeSaveOperation(true, 'Saved successfully');
  } catch (error) {
    console.error('Error saving quiz title:', error);
    globalStore.completeSaveOperation(false, 'Saved successfully');
  } finally {
    isSavingTitle.value = false;
  }
};

// // watcher สำหรับ autosave
// watch(editableTitle, (newTitle) => {
//   if (isAssessment.value && newTitle.trim()) {
//     void saveTitle(newTitle.trim());
//   }
// });

// const headerTitle = computed(() => {
//   if ((route.name === 'quiz-edit' || route.name === 'evaluate-edit') && route.params.id) {
//     if (asmTitle.value) {
//       return asmTitle.value;
//     }
//     const prefix = route.name === 'quiz-edit' ? 'แบบทดสอบ' : 'แบบสอบถาม';
//     return `${prefix} #${String(route.params.id)}`;
//   }
//   if (route.path.includes('/quiz')) {
//     return 'ระบบแบบทดสอบ';
//   }
//   if (route.path.includes('/evaluate')) {
//     return 'ระบบแบบสอบถาม';
//   }
//   if (route.path.includes('/ums')) {
//     return 'จัดการสิทธิ์ในระบบ';
//   }
//   return 'ระบบจัดการ';
// });

const isAssessment = computed(() => {
  return route.name === 'quiz-edit' || route.name === 'evaluate-edit';
});

const isQuizEditor = computed(() => {
  return route.name === 'quiz-edit';
});

const isEvaluateEditor = computed(() => {
  return route.name === 'evaluate-edit';
});

// ฟังก์ชันสำหรับดึง quiz title
const fetchQuizTitle = (quizId: string) => {
  try {
    // Use name from assessment in store
    const title = asmStore.currentAssessment?.name || '';
    asmTitle.value = title;
    editableTitle.value = title;
  } catch (error) {
    console.error('Error fetching quiz title:', error);
    const fallback = `แบบทดสอบ #${quizId}`;
    asmTitle.value = fallback;
    editableTitle.value = fallback;
  }
};

// ฟังก์ชันสำหรับ Evaluate Status Bar
// const onClickLink = () => {
//   console.log('Link clicked');
// };

const onClickPreview = async (row: Assessment) => {
  if (isQuizEditor.value) {
    await router.push({ name: 'quiz-preview', params: { id: row.id, section: 1 } });
    return;
  }
  if (isEvaluateEditor.value) {
    await router.push({ name: 'evaluate-preview', params: { id: row.id, section: 1 } });
    return;
  }
};
function openShareDialog(row: Assessment) {
  $q.dialog({
    component: defineAsyncComponent(() => import('./common/ShareLinkDialog.vue')),
    componentProps: {
      link: row.linkURL,
    },
    persistent: true,
  });
}
const onClickPublish = (row: Assessment) => {
  changStatusDialog.value?.openDialog(row.id, row.status, 'evaluate');
};

const onDuplicate = () => {
  blockCreatorStore.duplicateDialog = true;
  console.log('Duplicate', blockCreatorStore.duplicateDialog);
};

async function onDuplicateConfirm(id: number) {
  $q.loading.show({
    message: 'กำลังโหลดข้อมูล...',
    spinnerSize: 40,
  });
  console.log(id);
  if (blockCreatorStore.currentAssessment?.id)
    await new AssessmentService('evaluate').duplicate(id, blockCreatorStore.currentAssessment.id);
  $q.loading.hide();
  window.location.reload();
}

const isHomePage = computed(() => {
  return route.name === 'home';
});

// Debounced save on blur
const onTitleBlur = async () => {
  if (isAssessment.value && asmTitle.value.trim()) {
    await saveTitle(asmTitle.value.trim());
  }
};

// Watch for assessment load and update asmTitle
watch(
  () => asmStore.currentAssessment,
  (assessment) => {
    if (assessment && assessment.name) {
      asmTitle.value = assessment.name;
    }
  },
  { immediate: true },
);

// Watch for evaluate form store changes and sync with asmStore
watch(
  () => blockCreatorStore.currentAssessment,
  (assessment) => {
    const isEvaluateRoute = route.name === 'evaluate-edit' || route.path.includes('/evaluate');

    if (assessment && isEvaluateRoute) {
      asmStore.setCurrentAssessment(assessment);
      if (assessment.name) {
        asmTitle.value = assessment.name;
      }
    }
  },
  { immediate: true, deep: true },
);

// Watch for route changes to ensure proper assessment loading
watch(
  () => route.params.id,
  async (newId, oldId) => {
    if (newId && newId !== oldId) {
      // Clear previous assessment data
      if (!newId) {
        asmTitle.value = '';
        editableTitle.value = '';
        return;
      }

      // Handle evaluate routes
      if (route.name === 'evaluate-edit') {
        try {
          // Check if assessment is already loaded in blockCreatorStore
          if (blockCreatorStore.currentAssessment?.id === Number(newId)) {
            // Assessment already loaded, sync to asmStore
            asmStore.setCurrentAssessment(blockCreatorStore.currentAssessment);
            if (blockCreatorStore.currentAssessment.name) {
              asmTitle.value = blockCreatorStore.currentAssessment.name;
            }
          } else {
            // Load assessment if not already loaded
            await blockCreatorStore.fetchAssessmentById(Number(newId));
          }
        } catch (error) {
          console.error('Error loading assessment for route change:', error);
        }
      }

      // Handle quiz routes
      if (route.name === 'quiz-edit') {
        fetchQuizTitle(newId as string);
      }
    }
  },
  { immediate: true },
);

onMounted(async () => {
  globalStore.Loading();
  user.value = authStore.getCurrentUser();

  // Handle initial assessment loading based on route
  if (route.params.id) {
    if (route.name === 'quiz-edit') {
      fetchQuizTitle(route.params.id as string);
    } else if (route.name === 'evaluate-edit') {
      // For evaluate routes, ensure assessment is loaded
      try {
        if (
          !blockCreatorStore.currentAssessment ||
          blockCreatorStore.currentAssessment.id !== Number(route.params.id)
        ) {
          await blockCreatorStore.fetchAssessmentById(Number(route.params.id));
        }
      } catch (error) {
        console.error('Error loading assessment on mount:', error);
      }
    }
  }
});

const roles = computed(() => {
  if (user.value?.roles) {
    return user.value.roles.map((role) => ({
      label: role.name,
      value: role.name,
    }));
  }
  return [];
});
</script>

<template>
  <q-header>
    <q-toolbar id="main-toolbar">
      <!-- Left Section -->
      <q-toolbar-title class="flex items-center q-gutter-x-sm header-title-responsive">
        <q-btn
          padding="xs"
          style="background: transparent"
          icon="menu"
          color="black"
          flat
          @click="globalStore.toggleLeftDrawer()"
          class="menu-btn-mobile"
        ></q-btn>
        <img
          src="/brand/brand-og.svg"
          draggable="false"
          width="40px"
          height="40px"
          class="brand-logo-mobile"
        />
        <div
          class="text-black text-h6 q-ml-md cursor-pointer header-title-hover header-title-mobile"
        >
          <q-input
            v-if="isAssessment && !isHomePage"
            v-model="asmTitle"
            dense
            color="white"
            input-class="text-black"
            borderless
            class="bg-transparent header-title-input header-title-input-mobile"
            @blur="onTitleBlur"
            data-cy="asmTitleInput"
          >
            <template v-slot:append>
              <q-spinner-dots v-if="isSavingTitle" size="sm" color="white" />
            </template>
          </q-input>
          <div v-else class="row">
            <div class="col-12 text-weight-bold text-subtitle2">ระบบพัฒนาบุคลากร</div>
            <div class="col-12 text-weight-medium text-caption">มหาวิทยาลัยบูรพา</div>
          </div>
        </div>

        <!-- Global Save Status Indicator -->
        <div
          v-if="isAssessment && (globalStore.saveStatus !== 'idle' || isSavingTitle)"
          class="saving-container saving-container-mobile"
        >
          <div
            class="saving-box"
            :class="{
              'saving-box--success': globalStore.saveStatus === 'success',
              'saving-box--error': globalStore.saveStatus === 'error',
            }"
          >
            <q-spinner-dots
              v-if="globalStore.saveStatus === 'saving' || isSavingTitle"
              size="sm"
              color="white"
            />
            <q-icon
              v-else-if="globalStore.saveStatus === 'success'"
              name="check_circle"
              size="sm"
              color="white"
            />
            <q-icon
              v-else-if="globalStore.saveStatus === 'error'"
              name="error"
              size="sm"
              color="white"
            />
            <span class="saving-text">
              {{
                globalStore.saveStatus === 'saving' || isSavingTitle
                  ? 'กำลังบันทึก...'
                  : 'บันทึกเรียบร้อย'
              }}
            </span>
          </div>
        </div>
      </q-toolbar-title>

      <!-- Right Section -->
      <div class="row items-center q-gutter-sm right-section-mobile">
        <!-- Evaluate Action Buttons - แสดงเฉพาะในหน้า evaluate -->
        <div v-if="isAssessment" class="buttons-container q-mr-md buttons-container-mobile">
          <q-btn
            flat
            round
            icon="upload_file"
            size="md"
            class="text-black import-file-btn"
            @click="onDuplicate"
          >
            <q-tooltip class="bg-dark">คัดลอกฟอร์ม</q-tooltip></q-btn
          >
          <q-btn
            flat
            round
            icon="link"
            size="md"
            class="text-black icon-btn"
            @click="openShareDialog(asmStore.currentAssessment!)"
            ><q-tooltip class="bg-dark">คัดลอกลิงค์</q-tooltip></q-btn
          >
          <q-btn
            flat
            round
            icon="visibility"
            size="md"
            class="text-black icon-btn"
            @click="onClickPreview(asmStore.currentAssessment!)"
            ><q-tooltip class="bg-dark">ดูพรีวิว</q-tooltip></q-btn
          >
          <q-btn
            unelevated
            label="เผยแพร่"
            color="secondary
"
            @click="onClickPublish(asmStore.currentAssessment!)"
            class="publish-btn-improved publish-btn-mobile"
          ></q-btn>
        </div>

        <div
          class="row items-center q-mr-sm q-pa-sm rounded-borders bg-white switch-role-btn cursor-pointer"
        >
          <q-tooltip class="bg-dark" anchor="top left">สลับบทบาท</q-tooltip>
          <q-icon name="admin_panel_settings" size="sm"> </q-icon>
          <q-menu anchor="bottom right" self="top right">
            <q-list style="min-width: 200px">
              <q-item
                v-for="role in roles"
                :key="role.value"
                clickable
                v-close-popup
                @click="authStore.setCurrentRole(role.value)"
                :class="
                  authStore.currentRoleName === role.value ? 'text-blue text-weight-bold' : ''
                "
              >
                <q-item-section>
                  <q-item-label>{{ role.label }}</q-item-label>
                </q-item-section>
                <q-item-section side v-if="authStore.currentRoleName === role.value">
                  <q-icon name="check" color="blue" />
                </q-item-section>
              </q-item>
            </q-list>
          </q-menu>
          <div v-if="$q.screen.gt.sm" class="q-ml-sm">{{ authStore.currentRoleName }}</div>
        </div>
        <!-- User Section -->
        <div class="cursor-pointer row user-section-mobile">
          <q-tooltip class="bg-dark">โปรไฟล์</q-tooltip>
          <q-avatar class="q-mx-xs">
            <q-img src="/mockup/avatar.webp" />
          </q-avatar>

          <q-menu anchor="bottom right" self="top right">
            <q-card class="no-border q-ma-sm q-pa-md row items-center">
              <q-avatar rounded class="q-mx-auto" size="100px">
                <q-img style="border-radius: 99px" src="/mockup/avatar.webp" />
              </q-avatar>
              <div class="q-mt-sm col-12">
                <div class="text-subtitle1">{{ user?.name }}</div>
                <div class="col-12">
                  <q-chip v-for="item in user?.roles" :key="item.id" dense>{{ item.name }}</q-chip>
                </div>
              </div>
            </q-card>

            <q-separator spaced />

            <q-list>
              <q-item>
                <q-item-section avatar>
                  <q-icon name="badge" />
                </q-item-section>
                <q-item-section>
                  <q-item-label>อาจารย์ประจำ</q-item-label>
                  <q-item-label caption class="text-grey">ตำแหน่ง</q-item-label>
                </q-item-section>
              </q-item>

              <q-item>
                <q-item-section avatar>
                  <q-icon name="apartment" />
                </q-item-section>
                <q-item-section>
                  <q-item-label>คณะวิทยาการสารสนเทศ</q-item-label>
                  <q-item-label caption class="text-grey">หน่วยงาน</q-item-label>
                </q-item-section>
              </q-item>

              <q-item>
                <q-item-section avatar>
                  <q-icon name="schedule" />
                </q-item-section>
                <q-item-section>
                  <q-item-label>วันนี้ ตอนนี้</q-item-label>
                  <q-item-label caption class="text-grey">เข้าสู่ระบบล่าสุด</q-item-label>
                </q-item-section>
              </q-item>
            </q-list>

            <q-separator spaced />

            <q-list>
              <q-item clickable v-ripple @click="confirmLogout">
                <q-item-section avatar>
                  <q-icon name="logout" color="red" />
                </q-item-section>
                <q-item-section>
                  <q-item-label class="text-red">ออกจากระบบ</q-item-label>
                </q-item-section>
              </q-item>
            </q-list>
          </q-menu>
        </div>
      </div>
    </q-toolbar>
    <ConfirmDialog
      v-model="showLogoutConfirm"
      title="ยืนยันการออกจากระบบ"
      @confirm="handleLogoutConfirmed"
    />
  </q-header>

  <ChangStatusDialog ref="changStatusDialog" />
  <EvaluateDuplicateDialog
    @confirm="onDuplicateConfirm"
    @update:model-value="blockCreatorStore.duplicateDialog = false"
  />
</template>

<style scoped lang="scss">
.header-title-input {
  min-width: 350px;
}
.header-title-hover {
  transition: opacity 0.2s ease;
  user-select: none;
  width: 200px;
}

.header-title-hover:hover {
  opacity: 0.8;
  cursor: pointer;
}

.saving-container {
  position: relative;
  margin-left: 12px;
}

.saving-box {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: $generic-border-radius;
  padding: 6px 12px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.saving-box--success {
  background: var(--q-positive);
  border: 1px solid rgba($positive, 0.4);
}

.saving-box--error {
  background: var(--q-negative);
  border: 1px solid rgba($negative, 0.4);
}

.saving-text {
  color: $text-white;
  font-size: 0.75rem;
  font-weight: 500;
  opacity: 0.9;
  line-height: 1;
}

.animated-ellipsis {
  display: inline-flex;
  align-items: center;
  line-height: 1;
}

.animated-ellipsis .ellipsis-dot {
  color: $text-white;
  font-size: 0.75em;
  font-weight: bold;
  opacity: 0.8;
  animation-name: dot-wave-animation;
  animation-duration: 1.4s;
  animation-iteration-count: infinite;
  animation-timing-function: ease-in-out;
}

.animated-ellipsis .ellipsis-dot:nth-child(1) {
  animation-delay: 0s;
}
.animated-ellipsis .ellipsis-dot:nth-child(2) {
  animation-delay: 0.2s;
}
.animated-ellipsis .ellipsis-dot:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes dot-wave-animation {
  0%,
  60%,
  100% {
    transform: translateY(0);
    opacity: 0.7;
  }
  30% {
    transform: translateY(-4px);
    opacity: 1;
  }
}

/* Removed prefix 'evaluate-' from class names */
.buttons-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  height: 100%;
}

.icon-btn {
  width: 40px !important;
  height: 40px !important;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.icon-btn .q-btn__content {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.icon-btn:hover {
  background: rgba($white, 0.1);
  transform: translateY(-1px);
}

.publish-btn-improved {
  height: 40px !important;
  min-width: 100px;
  padding: 0 20px;
  color: $text-white !important;
  font-weight: 500;
  border-radius: 8px;
  font-size: 0.875rem;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba($black, 0.1);
}

.publish-btn-improved .q-btn__content {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.publish-btn-improved:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba($black, 0.15);
}

@media (max-width: 768px) {
  .q-toolbar {
    flex-wrap: wrap;
    padding-left: 4px;
    padding-right: 4px;
    min-height: 56px;
  }

  .header-title-responsive {
    flex-direction: row;
    align-items: center;
    width: 100%;
    min-width: 0;
    margin-bottom: 0;
    gap: 4px;
  }

  .brand-logo-mobile {
    width: 32px !important;
    height: 32px !important;
  }

  .header-title-hover,
  .header-title-mobile {
    font-size: 1.1rem !important;
    width: 120px !important;
    min-width: 0 !important;
    margin-left: 4px !important;
    margin-right: 0 !important;
    max-width: 120px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .header-title-input,
  .header-title-input-mobile {
    min-width: 120px !important;
    max-width: 120px !important;
    font-size: 1rem !important;
  }

  .saving-container,
  .saving-container-mobile {
    margin-left: 4px !important;
    padding: 0 !important;
    min-width: 0;
    font-size: 0.8rem;
  }

  .buttons-container,
  .buttons-container-mobile {
    gap: 4px !important;
    margin-right: 0 !important;
    padding: 0 !important;
  }

  .icon-btn,
  .import-file-btn {
    width: 32px !important;
    height: 32px !important;
    font-size: 1rem !important;
    border-radius: 6px !important;
  }

  .publish-btn-improved,
  .publish-btn-mobile {
    height: 32px !important;
    min-width: 70px !important;
    font-size: 0.8rem !important;
    padding: 0 10px !important;
    border-radius: 6px !important;
  }

  .user-section-mobile {
    .user-name-mobile {
      display: none !important;
    }
    .q-avatar {
      width: 32px !important;
      height: 32px !important;
      min-width: 32px !important;
      min-height: 32px !important;
    }
  }
}

@media (max-width: 480px) {
  .header-title-hover,
  .header-title-mobile {
    font-size: 0.95rem !important;
    width: 80px !important;
    max-width: 80px !important;
  }
  .header-title-input,
  .header-title-input-mobile {
    min-width: 80px !important;
    max-width: 80px !important;
    font-size: 0.95rem !important;
  }
  .publish-btn-improved,
  .publish-btn-mobile {
    min-width: 50px !important;
    font-size: 0.7rem !important;
    padding: 0 4px !important;
  }
}

#main-toolbar {
  // background-color: $primary; /* Dark background for the header */
  background: radial-gradient(circle at 0%, $white 20%, $primary 100%);
  color: $text-black; /* Text color */
  height: 60px;
  border-bottom: 1px solid $surface; /* Subtle border at the bottom */
}
</style>
