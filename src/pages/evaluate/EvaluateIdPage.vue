<template>
  <q-page class="q-pa-md">
    <div v-if="editFormData">
      <div
        v-for="(evaluateItem, index) in editFormData.itemBlocks?.filter(
          (item) => item.section === currentSection,
        )"
        :key="index"
      >
        <UserTextBlock
          v-if="evaluateItem.headerBody && evaluateItem.type === 'HEADER'"
          :item="evaluateItem"
          :title="evaluateItem.headerBody.title || 'ไม่มีข้อมูล'"
          :description="evaluateItem.headerBody.description || 'ไม่มีข้อมูล'"
        />
        <UserQuestionBlock
          v-if="evaluateItem.type !== 'IMAGE'"
          :id="evaluateItem.id"
          :draftId="submission?.id || 0"
          :item="editFormData"
          :category="evaluateItem.type"
          :section="evaluateItem.section"
          :status="isPreview"
          :clear="isClear"
          @update-answer="handleAnswer"
        />
        <UserImageBlock
          v-if="evaluateItem.type === 'IMAGE'"
          :title="evaluateItem.imageBody?.imageText || ''"
          :image-url="evaluateItem.imageBody?.imagePath || ''"
          @update:title="evaluateItem.headerBody && (evaluateItem.headerBody.title = $event)"
          @update:image-url="evaluateItem.imageBody && (evaluateItem.imageBody.imagePath = $event)"
          aria-label="dsda"
        />
      </div>
    </div>

    <div class="row q-pa-md btn-footer">
      <div class="col">
        <q-btn v-if="!isPreview" label="ล้างแบบสอบถาม" class="btn-clear" @click="clearForm" />
      </div>
      <div class="col-auto q-pr-lg">
        <q-btn v-if="currentSection > 1" label="กลับไปหน้าก่อน" @click="previousSection" />
      </div>
      <q-btn v-show="hideButton" label="หน้าต่อไป" @click="nextSection" />
      <div class="col-auto">
        <q-btn
          v-if="!hideButton && isPreview === false"
          label="ส่งแบบสอบถาม"
          @click="confirmDialog = true"
        />
      </div>
    </div>
    <SubmitDialog
      v-model="confirmDialog"
      :submitId="submission?.id ?? 0"
      :role="authStore.currentRoleName"
    />
  </q-page>
</template>

<script setup lang="ts">
import UserTextBlock from 'src/components/evaluate/UserEvaluateBlock/UserTextBlock.vue';
import UserImageBlock from 'src/components/evaluate/UserEvaluateBlock/UserImageBlock.vue';
import UserQuestionBlock from 'src/components/evaluate/UserEvaluateBlock/UserQuestionBlock.vue';
import { onMounted, ref, watchEffect } from 'vue';
import { AssessmentService } from 'src/services/asm/assessmentService';
import { summaryService } from 'src/services/asm/submissionService'; // หรือ path ที่คุณวางไฟล์จริงๆ
import type { Assessment, Submission, User } from 'src/types/models';
import { useRoute } from 'vue-router';
import { useAuthStore } from 'src/stores/auth';
import router from 'src/router';
import { onBeforeRouteUpdate } from 'vue-router';
import SubmitDialog from 'src/components/common/SubmitDialog.vue';

onMounted(async () => {
  // ป้องกัน NaN
  const sectionParam = Number(route.params.section);
  if (!isNaN(sectionParam)) {
    currentSection.value = sectionParam;
  }

  // ตรวจสอบ id ก่อนใช้
  const asmId = Number(route.query.id);
  const url = route.params.url;
  if (route.meta.status === 'do' && asmId && url) {
    isPreview.value = false;
    console.log(isPreview.value);
    user.value = authStore.getCurrentUser();

    const res = await new AssessmentService('evaluate').getAssessmentByUUID(String(url));
    editFormData.value = {
      ...res.data,
      itemBlocks:
        res.data.itemBlocks?.filter((item) => item.section === currentSection.value) ?? [],
    };

    if (route.name === 'evaluate-do' && user.value) {
      const sub = await summaryService.getDraft(asmId, user.value?.id);
      submission.value = sub;
      console.log(submission.value.id);
      console.log(asmId);
      if (!submission.value) {
        await summaryService.create({
          id: 0,
          assessmentId: asmId,
          userId: user.value?.id,
          startAt: '',
          endAt: '',
        });

        submission.value = await summaryService.getDraft(asmId, user.value?.id);
      }
      const checkSection =
        previous.value === true && currentSection.value !== 1
          ? currentSection.value - 1
          : currentSection.value + 1;

      checkItem.value = {
        ...res.data,
        itemBlocks: res.data.itemBlocks?.filter((item) => item.section === checkSection) ?? [],
      };

      hideButton.value = (checkItem.value?.itemBlocks?.length ?? 0) >= 1;
    }
  } else if (route.meta.status === 'preview' && route.params.id) {
    isPreview.value = true;
    const sectionParam = Number(route.params.section);
    if (!isNaN(sectionParam)) {
      currentSection.value = sectionParam;
    }
    console.log(currentSection.value);
    const asmId = Number(route.params.id);
    console.log(asmId);
    const res = await new AssessmentService('evaluate').fetchOne(asmId);

    editFormData.value = {
      ...res,
      itemBlocks: res.itemBlocks?.filter((item) => item.section === currentSection.value) ?? [],
    };
    console.log(editFormData.value);
    const checkSection =
      previous.value === true && currentSection.value !== 1
        ? currentSection.value - 1
        : currentSection.value + 1;

    checkItem.value = {
      ...res,
      itemBlocks: res.itemBlocks?.filter((item) => item.section === checkSection) ?? [],
    };

    hideButton.value = (checkItem.value?.itemBlocks?.length ?? 0) >= 1;
  }
});

onBeforeRouteUpdate(async (to) => {
  const sectionParam = Number(to.params.section);
  if (!isNaN(sectionParam)) {
    currentSection.value = sectionParam;
  }

  if (to.meta.status === 'do') {
    const url = String(to.params.url);
    user.value = authStore.getCurrentUser();

    const res = await new AssessmentService('evaluate').getAssessmentByUUID(url);

    editFormData.value = {
      ...res.data,
      itemBlocks:
        res.data.itemBlocks?.filter((item) => item.section === currentSection.value) ?? [],
    };
    const checkSection =
      previous.value === true && currentSection.value !== 1
        ? currentSection.value - 1
        : currentSection.value + 1;

    checkItem.value = {
      ...res.data,
      itemBlocks: res.data.itemBlocks?.filter((item) => item.section === checkSection) ?? [],
    };

    hideButton.value = (checkItem.value?.itemBlocks?.length ?? 0) >= 1;
  } else if (to.meta.status === 'preview') {
    console.log('preview');
    console.log(currentSection.value);

    const sectionParam = Number(to.params.section);
    const currentSec = !isNaN(sectionParam) ? sectionParam : 1;
    currentSection.value = currentSec;

    const res = await new AssessmentService('evaluate').fetchOne(Number(to.params.id));

    editFormData.value = {
      ...res,
      itemBlocks: res.itemBlocks?.filter((item) => item.section === currentSec) ?? [],
    };

    const checkSection =
      previous.value === true && currentSec !== 1 ? currentSec - 1 : currentSec + 1;

    checkItem.value = {
      ...res,
      itemBlocks: res.itemBlocks?.filter((item) => item.section === checkSection) ?? [],
    };

    hideButton.value = (checkItem.value?.itemBlocks?.length ?? 0) >= 1;
  }
});

const route = useRoute();
const checkItem = ref<Assessment>();
const editFormData = ref<Assessment>();
const isPreview = ref(false);
const evaluateId = ref(0);
const currentSection = ref(1);
const previous = ref(false);
//Submittion
const submission = ref<Submission>();
const user = ref<User>();
const authStore = useAuthStore();
const isClear = ref(false);

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const answers = ref<{ [key: string]: any }>({});

type AnswerValue = string | string[] | Record<string, number> | File[] | null;

const handleAnswer = (payload: { id: string | number; value: AnswerValue }) => {
  answers.value[payload.id] = payload.value;
};

watchEffect(() => {
  evaluateId.value = Number(route.query.id) || 0;
});

const hideButton = ref(true);

// เปลี่ยน section และโหลดข้อมูล พร้อม push query
const updateSection = async (newSection: number) => {
  currentSection.value = newSection;
  if (isPreview.value === true) {
    await router.push({
      name: 'evaluate-preview',
      params: {
        id: Number(route.params.id),
        section: currentSection.value,
      },
    });
  } else {
    await router.push({
      name: 'evaluate-do',
      params: {
        url: String(route.params.url),
        section: currentSection.value,
      },
      query: {
        id: route.query.id, // ใส่กลับไปให้เหมือนเดิม
      },
    });
  }
};

const nextSection = async () => {
  previous.value = false;
  await updateSection(currentSection.value + 1);
};

const previousSection = async () => {
  if (currentSection.value > 1) {
    currentSection.value--;
    previous.value = true;
    await updateSection(currentSection.value);
  }
};
const clearForm = () => {
  isClear.value = true;
};

const confirmDialog = ref(false);
// const submitForm = () => {
//   // if (user.value && submission.value) {
//   //   await summaryService.update(submission.value.id);
//   // }
//   confirmDialog.value = true;
// };
</script>

<style scoped lang="scss">
.btn-footer {
  margin: auto;
  max-width: 900px;
  min-width: 900px;
  width: 100%;
}

.btn-clear {
  background-color: white;
  color: $primary;
}
</style>
