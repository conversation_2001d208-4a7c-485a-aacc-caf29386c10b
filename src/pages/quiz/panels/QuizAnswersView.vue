<template>
  <div>
    <div v-if="isLoading" class="text-center q-my-xl">
      <q-spinner-dots color="primary" size="3em" />
      <div class="text-subtitle1 q-mt-sm">กำลังโหลดข้อมูลสรุปคำตอบ...</div>
    </div>

    <q-banner v-else-if="error" inline-actions class="text-white bg-red q-mb-md rounded-borders">
      เกิดข้อผิดพลาด: {{ error }}
      <template v-slot:action>
        <q-btn
          flat
          color="white"
          label="ลองใหม่"
          @click="fetchDataForThisComponent"
          :loading="isLoading"
        />
      </template>
    </q-banner>

    <div
      v-else-if="questionResponses && questionResponses.data && questionResponses.data.length > 0"
      class="q-mx-auto container-wrapper"
    >
      <q-card
        v-for="(question, index) in questionResponses.data as EnhancedQuestionResponseData[]"
        :key="question.questionId"
        class="q-pa-lg q-mb-xl no-border bg-grey-1"
      >
        <!-- คำถาม -->
        <div class="question-header">
          <div class="question-number">
            คำถามที่ {{ (paginationParams.page - 1) * paginationParams.limit + index + 1 }}
          </div>
          <div class="question-text">{{ formatText(question.questionText, 0) }}</div>
        </div>

        <div class="question-content">
          <div class="row q-col-gutter-x-lg justify-between items-stretch">
            <!-- Chart section with type selector -->
            <div
              v-if="
                question.questionType !== ItemBlockType.HEADER &&
                question.questionType !== ItemBlockType.IMAGE &&
                question.questionType !== ItemBlockType.TEXTFIELD
              "
              class="col-12 col-md-6"
            >
              <q-card class="no-border bg-white q-pa-md">
                <!-- ตัวเลือกประเภทกราฟและจำนวนการตอบกลับในส่วนหัว -->
                <div class="chart-header">
                  <div class="response-count">
                    <q-icon name="people" size="20px" class="q-mr-xs" />
                    <span>{{ getTotalResponses(question) }} การตอบ</span>
                  </div>
                  <div
                    v-if="question.questionType !== ItemBlockType.GRID"
                    class="chart-type-selector"
                  >
                    <DropDownChart
                      :model-value="selectedGraphType[question.questionId] || 'กราฟวงกลม'"
                      @update:modelValue="(val) => updateGraphType(question.questionId, val)"
                    />
                  </div>
                </div>

                <div class="chart-container">
                  <!-- Grid type questions always show as bar chart -->
                  <template v-if="question.questionType === ItemBlockType.GRID">
                    <Bar
                      :data="
                        generateBarChartData({
                          ...question,
                        })
                      "
                      :options="generateChartJsOptionsBar(question.options)"
                      :chart-id="`bar-chart-${question.questionId}`"
                      :dataset-id-key="`bar-dataset-${question.questionId}`"
                      :plugins="chartPlugins"
                      :key="`bar-${question.questionId}-grid-${JSON.stringify(question.gridData)}`"
                    />
                  </template>
                  <!-- Other question types can switch between pie and bar -->
                  <template v-else>
                    <template v-if="selectedGraphType[question.questionId] === 'กราฟวงกลม'">
                      <Pie
                        :data="
                          generatePieChartData({
                            ...question,
                          })
                        "
                        :options="generateChartJsOptionsPie(question.options)"
                        :chart-id="`pie-chart-${question.questionId}`"
                        :dataset-id-key="`pie-dataset-${question.questionId}`"
                        :plugins="chartPlugins"
                        :key="`pie-${question.questionId}-${selectedGraphType[question.questionId]}-${JSON.stringify(question)}`"
                      />
                    </template>
                    <template v-else-if="selectedGraphType[question.questionId] === 'กราฟแท่ง'">
                      <Bar
                        :data="
                          generateBarChartData({
                            ...question,
                            questionType:
                              question.questionType as (typeof ItemBlockType)[keyof typeof ItemBlockType],
                          })
                        "
                        :options="generateChartJsOptionsBar(question.options)"
                        :chart-id="`bar-chart-${question.questionId}`"
                        :dataset-id-key="`bar-dataset-${question.questionId}`"
                        :plugins="chartPlugins"
                        :key="`bar-${question.questionId}-${selectedGraphType[question.questionId]}-${JSON.stringify(question)}`"
                      />
                    </template>
                  </template>
                </div>
              </q-card>
            </div>

            <!-- Answer section -->
            <div class="col-12 col-md-6">
              <q-card class="q-pa-md no-border bg-white fit">
                <!-- Special display for TEXTFIELD questions -->
                <template v-if="question.questionType === ItemBlockType.TEXTFIELD">
                  <div class="text-responses q-mt-md">
                    <div class="text-h6 q-mb-md">
                      <q-icon name="format_quote" color="primary" />
                      คำตอบข้อความ ({{ question.textResponses?.length || 0 }})
                    </div>
                    <div
                      v-if="question.textResponses && question.textResponses.length > 0"
                      class="text-response-list"
                    >
                      <q-card
                        v-for="(response, index) in question.textResponses"
                        :key="index"
                        flat
                        bordered
                        class="response-card"
                      >
                        <q-card-section>
                          <div class="row items-center justify-between">
                            <div class="text-caption">คำตอบที่ #{{ index + 1 }}</div>
                          </div>
                          <div class="text-body1 q-mt-sm">"{{ formatText(response, 200) }}"</div>
                        </q-card-section>
                      </q-card>
                    </div>
                    <div v-else class="text-grey text-center q-pa-md">ยังไม่มีข้อมูลการตอบกลับ</div>
                  </div>
                </template>

                <!-- Special display for Grid questions -->
                <template
                  v-else-if="question.questionType === ItemBlockType.GRID && question.gridData"
                >
                  <div class="q-mt-md">
                    <div class="text-h6 q-mb-md">ตารางแสดงการตอบสนอง</div>
                    <div class="row items-center q-gutter">
                      <div
                        v-for="(option, index) in question.options"
                        :key="option.optionId"
                        class="q-pa-sm q-mb-sm rounded-borders row justify-between items-center choice-item"
                      >
                        <div class="choice-item-text">
                          {{ index + 1 }}. {{ formatText(option.optionText, 70) }}
                        </div>
                        <div class="choice-item-stats">
                          <div class="text-dark">
                            {{ option.selectionCount }}
                            ({{ getSelectionPercentage(question, option).toFixed(1) }}%)
                          </div>
                          <div class="text-caption text-grey-7">เลือก</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </template>

                <!-- Standard choice-based display -->
                <template v-else-if="question.options && question.options.length > 0">
                  <q-list class="q-gutter-y-md">
                    <q-item
                      v-for="(option, index) in question.options"
                      :key="option.optionId"
                      class="justify-between items-center choice-item"
                    >
                      <div
                        :class="option.isCorrectAnswer ? 'text-positive text-weight-medium' : ''"
                      >
                        {{ index + 1 }}. {{ formatText(option.optionText, 70) }}
                        <q-badge
                          v-if="option.isCorrectAnswer"
                          color="positive"
                          outline
                          label="ถูกต้อง"
                          class="q-ml-sm q-py-xs"
                        />
                      </div>
                      <div class="choice-item-stats">
                        <div
                          :class="
                            option.isCorrectAnswer ? 'text-positive text-weight-bold' : 'text-dark'
                          "
                        >
                          {{ option.selectionCount }}
                          ({{ getSelectionPercentage(question, option).toFixed(1) }}%)
                        </div>
                        <div class="text-caption text-grey-7">เลือก</div>
                      </div>
                    </q-item>
                  </q-list>
                </template>

                <!-- No responses message -->
                <div v-if="getTotalResponses(question) === 0" class="text-grey q-mt-sm">
                  ยังไม่มีการตอบกลับสำหรับคำถามนี้
                </div>
              </q-card>
            </div>
          </div>
        </div>
      </q-card>

      <!-- Pagination Controls -->
      <div
        v-if="questionResponses && questionResponses.total > 0"
        class="row justify-center q-mt-xl"
      >
        <div class="col-12 text-center q-mb-md">
          <div class="text-body2 text-grey-7">
            แสดงคำถามที่ {{ (questionResponses.curPage - 1) * paginationParams.limit + 1 }}-{{
              Math.min(questionResponses.curPage * paginationParams.limit, questionResponses.total)
            }}
            จากทั้งหมด {{ questionResponses.total }} คำถาม
          </div>
        </div>
        <q-pagination
          v-model="paginationParams.page"
          :max="Math.ceil(questionResponses.total / paginationParams.limit)"
          :max-pages="6"
          :boundary-links="true"
          :direction-links="true"
          color="black"
          @update:model-value="handlePageChange"
        />
      </div>
    </div>

    <!-- Empty states -->
    <div v-else-if="!isLoading && !error" class="text-center text-grey q-my-xl">
      <q-icon name="sentiment_dissatisfied" size="3em" />
      <div class="text-subtitle1 q-mt-sm">ไม่มีข้อมูลสรุปคำตอบ</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, watch, onMounted, reactive } from 'vue';
import DropDownChart from 'src/components/DropDownChart.vue';

// ** Chart.js Imports **
import { Bar, Pie } from 'vue-chartjs';
import {
  Chart as ChartJS,
  Title,
  Tooltip,
  Legend,
  BarElement,
  CategoryScale,
  LinearScale,
  ArcElement,
  type ChartData,
  type ChartOptions,
  type Plugin,
} from 'chart.js';
import ChartDataLabels from 'chartjs-plugin-datalabels';
import type { Context } from 'chartjs-plugin-datalabels';
import { useQuizDashboardStore } from 'src/stores/quizdashboardStore';
import type { QuestionResponseData, OptionResponseData } from 'src/types/quiz-dashboard';

// Define the ItemBlockType enum for use in the template and code
const ItemBlockType = {
  RADIO: 'RADIO',
  CHECKBOX: 'CHECKBOX',
  TEXTFIELD: 'TEXTFIELD',
  GRID: 'GRID',
  HEADER: 'HEADER',
  IMAGE: 'IMAGE',
  UPLOAD: 'UPLOAD',
};
interface GridData {
  rows: string[];
  columns: string[];
  responses: Record<string, Record<string, number>>;
}

interface UploadResponse {
  fileUrl: string;
  fileName: string;
  count: number;
}

interface EnhancedQuestionResponseData extends QuestionResponseData {
  questionId: number;
  orderInQuiz: number;
  questionText: string;
  questionType: (typeof ItemBlockType)[keyof typeof ItemBlockType];
  options: OptionResponseData[];
  gridData?: GridData;
  textResponses?: string[];
  uploadResponses?: UploadResponse[];
}

// Enum for question types
// const ItemBlockType = {
//   RADIO: 'RADIO' as const,
//   CHECKBOX: 'CHECKBOX' as const,
//   TEXTFIELD: 'TEXTFIELD' as const,
//   GRID: 'GRID' as const,
//   HEADER: 'HEADER' as const,
//   IMAGE: 'IMAGE' as const,
//   UPLOAD: 'UPLOAD' as const,
// } as const;

ChartJS.register(
  Title,
  Tooltip,
  Legend,
  BarElement,
  CategoryScale,
  LinearScale,
  ArcElement,
  ChartDataLabels,
);

const props = defineProps<{
  assessmentId: number | null;
}>();

const dashboardStore = useQuizDashboardStore();
const selectedGraphType = reactive<Record<string | number, 'กราฟแท่ง' | 'กราฟวงกลม'>>({});

// Pagination parameters
const paginationParams = reactive({
  page: 1,
  limit: 10,
  search: '',
  sortBy: 'orderInQuiz',
  order: 'ASC' as const,
});

const isLoading = computed(() => dashboardStore.isLoadingQuestions);
const error = computed(() => dashboardStore.error);
const questionResponses = computed(() => dashboardStore.questionResponses);

// const formatText = (text: string, maxLength = 180): string => {
//   if (!text) return '';
//   if (maxLength === 0) return text;
//   if (text.length > maxLength) {
//     return text.substring(0, maxLength) + '...';
//   }
//   return text;
// };

const formatText = (text: string, maxLength = 180): string => {
  if (!text) return '';

  // 🔹 ลบ HTML tags ออก เช่น <div>, <br>, <span>
  const cleanText = text.replace(/<[^>]*>/g, '').trim();

  // 🔹 ถ้าไม่ต้องการตัดข้อความ
  if (maxLength === 0) return cleanText;

  // 🔹 ตัดข้อความถ้ายาวเกิน
  return cleanText.length > maxLength ? cleanText.substring(0, maxLength) + '...' : cleanText;
};

// Helper functions for data transformation
const getTotalResponses = (question: QuestionResponseData): number => {
  if (!question.options) return 0;
  return question.options.reduce((sum, option) => sum + option.selectionCount, 0);
};

const getSelectionPercentage = (
  question: QuestionResponseData,
  option: OptionResponseData,
): number => {
  const total = getTotalResponses(question);
  if (total === 0) return 0;
  return (option.selectionCount / total) * 100;
};

// Define the specific color for the correct answer
const correctChoiceColor = 'rgba(52, 168, 83, 0.75)';

// Define an ordered list of colors for other (incorrect) choices using a modern gradient palette
const otherChoiceBaseColors = [
  'rgba(66, 133, 244, 0.85)',
  'rgba(251, 188, 5, 0.75)', // Vibrant Yellow
  'rgba(234, 67, 53, 0.75)', // Vibrant Red
  'rgba(66, 133, 244, 0.75)', // Vibrant Blue
  'rgba(156, 39, 176, 0.75)', // Vibrant Purple
  'rgba(0, 150, 136, 0.75)', // Vibrant Teal
  'rgba(233, 30, 99, 0.75)', // Vibrant Pink
  'rgba(96, 125, 139, 0.75)', // Vibrant Blue Grey
  'rgba(255, 152, 0, 0.75)', // Vibrant Orange
  'rgba(121, 85, 72, 0.75)', // Vibrant Brown
];

const chartPlugins: Plugin[] = [ChartDataLabels];

const generatePieChartData = (question: EnhancedQuestionResponseData): ChartData<'pie'> => {
  if (!question || !question.options || question.options.length === 0) {
    return {
      labels: ['ไม่มีข้อมูล'],
      datasets: [
        {
          label: 'ไม่มีการเลือก',
          data: [1],
          backgroundColor: ['#E0E0E0'],
          borderWidth: 0,
        },
      ],
    };
  }

  // ตรวจสอบประเภทคำถาม
  switch (question.questionType) {
    case ItemBlockType.RADIO:
    case ItemBlockType.CHECKBOX: {
      const labels = question.options.map(
        (o) => `${o.orderInQuestion}. ${formatText(o.optionText, 15)}`,
      );
      const data = question.options.map((o) => o.selectionCount);
      const colors = getChartColors(question.options);

      return {
        labels: labels,
        datasets: [
          {
            label: 'การเลือกตอบ',
            data: data,
            backgroundColor: colors,
            borderWidth: 0,
            hoverBackgroundColor: colors.map((color) => (color ?? '').replace('0.75)', '0.9)')),
            hoverBorderWidth: 0,
          },
        ],
      };
    }

    case ItemBlockType.GRID: {
      if (question.gridData) {
        const { rows, columns, responses } = question.gridData;
        const datasets = columns.map((col, index) => ({
          label: col,
          data: rows.map((row) => responses[row]?.[col] || 0),
          backgroundColor: otherChoiceBaseColors[index % otherChoiceBaseColors.length],
          borderWidth: 0,
        }));

        return {
          labels: rows,
          datasets,
        } as ChartData<'pie'>;
      }
      break;
    }

    case ItemBlockType.TEXTFIELD: {
      if (question.textResponses) {
        return {
          labels: ['ตอบกลับ', 'ไม่ตอบ'],
          datasets: [
            {
              data: [
                question.textResponses.length,
                getTotalResponses(question) - question.textResponses.length,
              ],
              backgroundColor: [correctChoiceColor, 'rgba(200, 200, 200, 0.75)'],
              borderWidth: 0,
            },
          ],
        };
      }
      break;
    }

    case ItemBlockType.UPLOAD: {
      if (question.uploadResponses) {
        return {
          labels: ['อัพโหลดแล้ว', 'ยังไม่ได้อัพโหลด'],
          datasets: [
            {
              data: [
                question.uploadResponses.reduce((sum, item) => sum + item.count, 0),
                getTotalResponses(question) -
                  question.uploadResponses.reduce((sum, item) => sum + item.count, 0),
              ],
              backgroundColor: [correctChoiceColor, 'rgba(200, 200, 200, 0.75)'],
              borderWidth: 0,
            },
          ],
        };
      }
      break;
    }
  }

  const labels = question.options.map(
    (o) => `${o.orderInQuestion}. ${formatText(o.optionText, 15)}`,
  );
  const data = question.options.map((o) => o.selectionCount);

  let otherColorIndex = 0;
  const backgroundColors = question.options.map((option) => {
    if (option.isCorrectAnswer) {
      return correctChoiceColor;
    } else {
      const color = otherChoiceBaseColors[otherColorIndex % otherChoiceBaseColors.length];
      otherColorIndex++;
      return color;
    }
  });

  return {
    labels: labels,
    datasets: [
      {
        label: 'การเลือกตอบ',
        data: data,
        backgroundColor: backgroundColors,
        borderWidth: 0,
        hoverBackgroundColor: backgroundColors.map((color) =>
          (color ?? '').replace('0.75)', '0.9)'),
        ),
        hoverBorderWidth: 0,
      },
    ],
  };
};

const generateBarChartData = (question: EnhancedQuestionResponseData): ChartData<'bar'> => {
  if (!question || !question.options || question.options.length === 0) {
    return {
      labels: ['ไม่มีข้อมูล'],
      datasets: [
        {
          label: 'ไม่มีข้อมูล',
          data: [0],
          backgroundColor: ['rgba(200, 200, 200, 0.75)'],
          borderColor: ['rgba(150, 150, 150, 0.75)'],
          borderWidth: 1,
        },
      ],
    };
  }

  switch (question.questionType) {
    case ItemBlockType.GRID: {
      if (question.gridData) {
        const { rows, columns, responses } = question.gridData;
        return {
          labels: rows,
          datasets: columns.map((col, index) => ({
            label: col,
            data: rows.map((row) => responses[row]?.[col] || 0),
            backgroundColor: otherChoiceBaseColors[index % otherChoiceBaseColors.length],
            borderWidth: 0,
            borderRadius: 8,
          })),
        };
      }
      break;
    }

    case ItemBlockType.RADIO:
    case ItemBlockType.CHECKBOX: {
      const labels = question.options.map((o) => formatText(o.optionText, 20));
      const data = question.options.map((o) => o.selectionCount);
      const colors = getChartColors(question.options);

      return {
        labels: labels,
        datasets: [
          {
            label: 'จำนวนผู้เลือก',
            data: data,
            backgroundColor: colors,
            borderWidth: 0,
            borderRadius: 8,
          },
        ],
      };
    }

    case ItemBlockType.TEXTFIELD: {
      if (question.textResponses) {
        return {
          labels: ['ตอบกลับ', 'ไม่ตอบ'],
          datasets: [
            {
              label: 'สถานะการตอบ',
              data: [
                question.textResponses.length,
                getTotalResponses(question) - question.textResponses.length,
              ],
              backgroundColor: [correctChoiceColor, 'rgba(200, 200, 200, 0.75)'],
              borderWidth: 0,
              borderRadius: 8,
            },
          ],
        };
      }
      break;
    }

    case ItemBlockType.UPLOAD: {
      if (question.uploadResponses) {
        return {
          labels: ['อัพโหลดแล้ว', 'ยังไม่ได้อัพโหลด'],
          datasets: [
            {
              label: 'สถานะการอัพโหลด',
              data: [
                question.uploadResponses.reduce((sum, item) => sum + item.count, 0),
                getTotalResponses(question) -
                  question.uploadResponses.reduce((sum, item) => sum + item.count, 0),
              ],
              backgroundColor: [correctChoiceColor, 'rgba(200, 200, 200, 0.75)'],
              borderColor: [correctChoiceColor, 'rgba(200, 200, 200, 0.75)'],
              borderWidth: 1,
              borderRadius: 8,
            },
          ],
        };
      }
      break;
    }
  }

  // Default fallback
  return {
    labels: ['ไม่มีข้อมูล'],
    datasets: [
      {
        label: 'ไม่มีข้อมูล',
        data: [0],
        backgroundColor: ['rgba(200, 200, 200, 0.75)'],
        borderColor: ['rgba(150, 150, 150, 0.75)'],
        borderWidth: 1,
      },
    ],
  };

  // unreachable code removed: labels, data, and backgroundColors are not defined here
};

const generateChartJsOptionsBar = (
  options: OptionResponseData[] | undefined,
): ChartOptions<'bar'> => {
  return generateChartJsOptionsBase(options, 'bar') as ChartOptions<'bar'>;
};

const generateChartJsOptionsPie = (
  options: OptionResponseData[] | undefined,
): ChartOptions<'pie'> => {
  return generateChartJsOptionsBase(options, 'pie') as ChartOptions<'pie'>;
};

const generateChartJsOptionsBase = (
  options: OptionResponseData[] | undefined,
  type: 'bar' | 'pie',
): ChartOptions<'bar' | 'pie'> => {
  const isBarChart = type === 'bar';
  const totalResponsesForOptions =
    options?.reduce((sum, option) => sum + option.selectionCount, 0) || 0;

  const chartOptions: ChartOptions<'bar' | 'pie'> = {
    responsive: true,
    maintainAspectRatio: false,
    scales: {},
    layout: {
      padding: {
        top: isBarChart ? 30 : 10,
        bottom: 10,
        left: 10,
        right: 10,
      },
    },
    datasets: {
      bar: {
        barPercentage: 0.75,
        categoryPercentage: 0.85,
        borderRadius: 8,
        borderSkipped: false,
      },
    },
    transitions: {
      active: {
        animation: {
          duration: 400,
        },
      },
    },
    plugins: {
      legend: {
        display: !isBarChart,
        position: 'bottom',
        labels: {
          font: {
            size: 11,
            family: "'Kanit', sans-serif",
          },
          boxWidth: 15,
          padding: 15,
          usePointStyle: true,
          color: '#424242',
        },
      },
      title: {
        display: false,
      },
      tooltip: {
        enabled: true,
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
        titleColor: '#333',
        bodyColor: '#333',
        titleFont: {
          family: "'Kanit', sans-serif",
          size: 13,
          weight: 600,
        },
        bodyFont: {
          family: "'Kanit', sans-serif",
          size: 12,
        },
        padding: { x: 12, y: 8 },
        borderWidth: 1,
        borderColor: 'rgba(0,0,0,0.1)',
        callbacks: {
          label: function (context) {
            const optionText = options?.[context.dataIndex]?.optionText || context.label || '';
            let label = formatText(optionText, 45);
            const value = context.parsed.y ?? context.parsed;
            if (options && context.dataIndex < options.length) {
              const option = options[context.dataIndex];
              if (option) {
                const percentage = (option.selectionCount / totalResponsesForOptions) * 100;
                label = `${label}\nจำนวน: ${value} คน (${percentage.toFixed(1)}%)`;
              }
            }
            return label;
          },
        },
        displayColors: false,
      },
      datalabels: {
        color: (context: Context) => {
          if (type === 'pie') {
            const bgColors = Array.isArray(context.dataset.backgroundColor)
              ? context.dataset.backgroundColor
              : [context.dataset.backgroundColor];
            const bgColor = bgColors[context.dataIndex % bgColors.length];
            if (typeof bgColor === 'string') {
              const r = parseInt(bgColor.slice(1, 3), 16);
              const g = parseInt(bgColor.slice(3, 5), 16);
              const b = parseInt(bgColor.slice(5, 7), 16);
              return r * 0.299 + g * 0.587 + b * 0.114 > 170 ? '#333333' : '#FFFFFF';
            }
            return '#FFFFFF';
          }
          return '#333333';
        },
        anchor: isBarChart ? 'end' : 'center',
        align: isBarChart ? 'top' : 'center',
        offset: isBarChart ? -10 : 0,
        font: {
          family: "'Kanit', sans-serif",
          weight: 500,
          size: 12,
        },
        backgroundColor: () => {
          if (isBarChart) {
            return 'rgba(255, 255, 255, 0.92)';
          }
          return null;
        },
        borderRadius: 6,
        padding: { top: 5, bottom: 5, left: 8, right: 8 },
        formatter: (value: number) => {
          if (totalResponsesForOptions === 0 || value === 0) return '';
          const percentage = (value / totalResponsesForOptions) * 100;
          if (percentage < 3 && type === 'pie') return '';
          return `${value} (${percentage.toFixed(0)}%)`;
        },
        display: (context: Context) => {
          const value = context.dataset.data[context.dataIndex];
          return typeof value === 'number' && value > 0;
        },
      },
    },
  };

  if (isBarChart) {
    const maxValue = options ? Math.max(0, ...options.map((o) => o.selectionCount)) : 0;
    let stepSize = 1;
    if (maxValue > 10) {
      stepSize = Math.ceil(maxValue / 5);
    } else if (maxValue > 0) {
      stepSize = Math.max(1, Math.floor(maxValue / 2)) || 1;
    }

    const barScales: ChartOptions<'bar'>['scales'] = {
      y: {
        beginAtZero: true,
        border: {
          display: false,
        },
        ticks: {
          stepSize: stepSize,
          precision: 0,
          padding: 8,
          font: {
            size: 12,
            family: "'Kanit', sans-serif",
            weight: 400,
          },
          color: '#666666',
        },
        grid: {
          color: 'rgba(0, 0, 0, 0.04)',
          lineWidth: 1,
        },
      },
      x: {
        border: {
          display: false,
        },
        ticks: {
          font: {
            size: 12,
            family: "'Kanit', sans-serif",
            weight: 400,
          },
          maxRotation: 25,
          minRotation: 0,
          autoSkipPadding: 15,
          display: true,
          color: '#666666',
          padding: 8,
        },
        grid: {
          display: false,
        },
      },
    };

    if (chartOptions.datasets) {
      chartOptions.datasets.bar = {
        ...(chartOptions.datasets.bar || {}),
        barPercentage: 1.0,
        categoryPercentage: 0.95,
      };
    } else {
      chartOptions.datasets = { bar: { barPercentage: 1.0, categoryPercentage: 0.95 } };
    }
    chartOptions.scales = barScales || {};
  }

  return chartOptions;
};

async function fetchDataForThisComponent() {
  if (props.assessmentId !== null) {
    await dashboardStore.fetchQuestionResponses(props.assessmentId, paginationParams);
  }
}

async function handlePageChange(newPage: number) {
  paginationParams.page = newPage;
  await fetchDataForThisComponent();
}

function updateGraphType(questionId: number, newType: 'กราฟแท่ง' | 'กราฟวงกลม') {
  selectedGraphType[questionId] = newType;
}

onMounted(async () => {
  await fetchDataForThisComponent();
});

watch(
  () => props.assessmentId,
  async () => {
    await fetchDataForThisComponent();
  },
);

watch(
  () => questionResponses.value,
  (newResponses) => {
    if (newResponses && newResponses.data) {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      newResponses.data.forEach((q: { options: string | any[]; questionId: string | number }) => {
        if (q.options && q.options.length > 0 && selectedGraphType[q.questionId] === undefined) {
          selectedGraphType[q.questionId] = 'กราฟแท่ง'; // Default to bar chart
        }
      });
    } else if (props.assessmentId === null) {
      // Clear selected graph types when assessment ID is null
      for (const key in selectedGraphType) {
        delete selectedGraphType[key];
      }
    }
  },
  { immediate: true, deep: true },
);

const getChartColors = (options: OptionResponseData[]) => {
  let otherColorIndex = 0;
  return options.map((option) => {
    if (option.isCorrectAnswer) {
      return correctChoiceColor;
    } else {
      const color = otherChoiceBaseColors[otherColorIndex % otherChoiceBaseColors.length];
      otherColorIndex++;
      return color;
    }
  });
};
</script>

<style scoped lang="scss">
.container-wrapper {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 24px;
}

.question-card {
  padding: 24px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.04);

  &:hover {
    box-shadow: 0 6px 24px rgba(0, 0, 0, 0.06);
  }
}

.question-header {
  display: grid;
  grid-template-columns: auto 1fr;
  gap: 16px;
  margin-bottom: 20px;
  align-items: baseline;
}

.question-number {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 6px 12px;
  border-radius: 8px;
  font-weight: 600;
  font-size: 0.95rem;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.12);
  width: fit-content;
  margin-top: 2px;
}

.question-text {
  font-size: 1.1rem;
  line-height: 1.5;
  color: #2c3e50;
  font-weight: 500;
  min-width: 0;
}

.response-count {
  display: inline-flex;
  align-items: center;
  background: #f8fafc;
  padding: 8px 16px;
  border-radius: 24px;
  color: #64748b;
  font-size: 0.9rem;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.03);
  margin: 0;
}

.question-content {
  margin-top: 20px;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.chart-container {
  position: relative;
  height: 300px;
  width: 100%;
  box-sizing: border-box;
  background: #ffffff;
  border-radius: 8px;
  padding: 12px;
}

.choice-item {
  border: $grey-4 1px solid;
  font-size: 14px;
  min-height: 54px;
  width: 100%;
  border-radius: $generic-border-radius;
}

.choice-item-text {
  white-space: pre-wrap;
  word-break: break-word;
  flex: 1;
  margin-right: 16px;
  line-height: 1.5;
  color: #2c3e50;
  font-weight: 500;

  &.text-positive {
    color: #10b981;
  }
}

.choice-item-stats {
  white-space: nowrap;
  text-align: right;
  flex-shrink: 0;
  padding: 0 8px;

  .text-dark {
    font-weight: 600;
    color: #1a237e;
    font-size: 1.1rem;
  }

  .text-grey-7 {
    font-size: 0.85rem;
    color: #64748b;
  }
}

/* Empty state styling */
.text-center.text-grey.q-my-xl {
  padding: 48px 0;

  .q-icon {
    margin-bottom: 16px;
    opacity: 0.7;
  }

  .text-subtitle1 {
    color: #64748b;
    font-weight: 500;
  }
}

/* Error banner styling */
.q-banner.bg-red {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.2);

  .q-btn {
    border-radius: 8px;
    font-weight: 500;

    &:hover {
      background: rgba(255, 255, 255, 0.1);
    }
  }
}

/* Add beautiful animation for loading state */
.q-spinner-dots {
  animation: fadeInScale 0.5s ease-out;
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}
</style>
