import {
  ForbiddenException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';

import { InjectEntityManager, InjectRepository } from '@nestjs/typeorm';
import { EntityManager, Repository } from 'typeorm';
import { Assessment } from '../entities/assessment.entity';
import { Response } from '../entities/response.entity';
import type { UpdateResponseDto } from '../dto/updates/update-response.dto';
import type { CreateResponseDto } from '../dto/creates/create-response.dto';
import { ResponsesHelper } from './responses.helper';

@Injectable()
export class ResponsesService {
  constructor(
    @InjectEntityManager()
    private entityManager: EntityManager,
    @InjectRepository(Assessment)
    private assessmentRepo: Repository<Assessment>,
    @InjectRepository(Response)
    private responseRepo: Repository<Response>,
    private responsesHelper: ResponsesHelper,
  ) {}

  async create(createResponseDto: CreateResponseDto): Promise<Response> {
    const response = this.responseRepo.create(createResponseDto);
    return await this.responseRepo.save(response);
  }

  findAll() {
    return `This action returns all responses`;
  }

  findOne(id: number) {
    return `This action returns a #${id} response`;
  }

  async findEvaluateAnswer(
    submission: number,
    question: number,
  ): Promise<Response | {}> {
    const response = await this.responseRepo.findOne({
      where: { submissionId: submission, questionId: question },
      relations: ['question', 'selectedOption'],
    });

    return response || {}; // ถ้าไม่เจอจะคืน {}
  }

  async findEvaluateAnswers(
    submissionId: number,
    questionId: number,
  ): Promise<Response[]> {
    return this.responseRepo.find({
      where: { submissionId, questionId },
      relations: ['question', 'selectedOption'],
    });
  }

  async findEvaluateCheckBoxAnswer(
    submission: number,
    question: number,
    selectedOption: number,
  ) {
    const responese = await this.responseRepo.findOne({
      where: {
        submissionId: submission,
        questionId: question,
        selectedOptionId: selectedOption,
      },
    });

    return responese ?? {}; // ถ้าไม่เจอให้คืน {}
  }

  findAllBySubmissionId(submissionId: number) {
    return this.responseRepo.find({
      where: { submissionId },
      relations: { selectedOption: true },
    });
  }

  async update(
    id: number,
    updateResponseDto: UpdateResponseDto,
  ): Promise<Response> {
    await this.responseRepo.update(id, updateResponseDto);
    return await this.responseRepo.findOneOrFail({
      where: { id },
      relations: ['submission', 'selectedOption', 'question'],
    });
  }

  async remove(id: number): Promise<void> {
    await this.responseRepo.delete(id);
  }

  async clear(submissionId: number): Promise<void> {
    await this.responseRepo.delete({ submissionId });
  }

  async userSaveQuizResponse(createResponseDto: CreateResponseDto) {
    const {
      submissionId,
      questionId,
      answerText,
      selectedOptionId,
      responseId,
    } = createResponseDto;

    // ✅ Validate submission
    await this.responsesHelper.validateSubmission(submissionId);

    // ✅ ถ้าเป็นการตอบแบบ textAnswer (answerText)
    let finalSelectedOptionId = selectedOptionId;
    if (answerText) {
      finalSelectedOptionId =
        await this.responsesHelper.saveOrUpdateOptionForTextAnswer(
          answerText,
          selectedOptionId,
          questionId,
        );
    }

    // ✅ Save or update response
    const savedResponse = await this.responsesHelper.saveOrUpdateResponse(
      submissionId,
      questionId,
      finalSelectedOptionId,
      responseId,
    );

    return savedResponse;
  }
}
