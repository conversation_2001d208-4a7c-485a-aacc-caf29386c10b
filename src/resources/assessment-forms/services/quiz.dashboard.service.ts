import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Like } from 'typeorm';
import type { DataParams, DataResponse } from 'src/types/params';
import { Submission } from '../entities/submission.entity';
import { Question } from '../entities/question.entity';
import { Assessment } from '../entities/assessment.entity';
import { AssessmentValidator } from '../helper/assessments.validator';

export interface AssessmentMeta {
  assessmentName: string;
  uniqueUsers: number;
  highestScore: number;
  lowestScore: number;
}

export interface ChartData {
  labels: string[];
  datasets: {
    data: number[];
    backgroundColor: string[];
  }[];
}

export interface QuestionResponseData {
  questionId: number;
  orderInQuiz: number;
  questionText: string;
  questionType: string;
  options: OptionResponseData[];
  chartData: {
    labels: string[];
    datasets: {
      data: number[];
      backgroundColor: string[];
    }[];
  };
}

export interface OptionResponseData {
  optionId: number;
  orderInQuestion: number;
  optionText: string;
  selectionCount: number;
  isCorrectAnswer: boolean;
}

export interface ParticipantData {
  id: number;
  date: string;
  userName: string;
  score: number;
}

@Injectable()
export class QuizDashboardService {
  constructor(
    @InjectRepository(Submission)
    private submissionRepository: Repository<Submission>,
    @InjectRepository(Question)
    private questionRepository: Repository<Question>,
    @InjectRepository(Assessment)
    private assessmentRepository: Repository<Assessment>,
    private assessmentValidator: AssessmentValidator,
  ) {}

  async generateAssessmentMeta(
    assessmentId: number,
    assessmentName?: string,
  ): Promise<AssessmentMeta> {
    await this.assessmentValidator.validateAssessment(assessmentId);

    const [assessment, uniqueUsers, scoreStats] = await Promise.all([
      this.assessmentRepository.findOne({ where: { id: assessmentId } }),
      this.getUniqueUsersCount(assessmentId),
      this.getScoreStatistics(assessmentId),
    ]);

    return {
      assessmentName: assessment?.name || `Assessment #${assessmentId}`,
      uniqueUsers,
      ...scoreStats,
    };
  }

  async generateScoreDistributionChart(
    assessmentId: number,
  ): Promise<ChartData> {
    await this.assessmentValidator.validateAssessment(assessmentId);

    const submissionCount = await this.submissionRepository.count({
      where: { assessmentId },
    });

    if (submissionCount === 0) {
      return this.createEmptyChart();
    }

    const scoreDistribution = await this.getScoreDistribution(assessmentId);
    return this.formatScoreChart(scoreDistribution);
  }

  async generateAllQuestionResponses(
    assessmentId: number,
    query: DataParams,
  ): Promise<DataResponse<QuestionResponseData>> {
    await this.assessmentValidator.validateAssessment(assessmentId);

    const questionsData = await this.getQuestionsForAssessment(
      assessmentId,
      query,
    );

    if (questionsData.total === 0) {
      return {
        data: [],
        total: 0,
        curPage: questionsData.curPage,
        hasPrev: false,
        hasNext: false,
      };
    }

    const responses = await Promise.all(
      questionsData.data.map((question) =>
        this.buildQuestionResponse(question),
      ),
    );

    return {
      data: responses,
      total: questionsData.total,
      curPage: questionsData.curPage,
      hasPrev: questionsData.hasPrev,
      hasNext: questionsData.hasNext,
    };
  }
  async getAllParticipants(
    assessmentId: number,
    query: DataParams,
  ): Promise<DataResponse<ParticipantData>> {
    await this.assessmentValidator.validateAssessment(assessmentId);

    const { page = 1, limit = 10, sortBy, order, search } = query;
    const skip = (Number(page) - 1) * Number(limit);

    const whereCondition: any = { assessmentId };
    const relations = [
      'user',
      'responses',
      'responses.selectedOption',
      'responses.question',
    ];

    if (search) {
      whereCondition.user = { name: Like(`%${search}%`) };
    }    const orderCondition: any = {};
    if (sortBy === 'userName') {
      orderCondition.user = { name: (order || 'ASC').toUpperCase() };
    } else if (sortBy === 'date') {
      orderCondition.startAt = (order || 'ASC').toUpperCase();
    } else if (sortBy === 'score') {
    } else if (sortBy) {
      orderCondition[sortBy] = (order || 'ASC').toUpperCase();
    } else {
      orderCondition.id = 'ASC';
    }

    const [submissions, total] = await this.submissionRepository.findAndCount({
      where: whereCondition,
      relations,
      order: orderCondition,
      take: Number(limit),
      skip,
    });    let formattedParticipants = submissions.map((submission) => {
      const score =
        submission.responses?.reduce((total, response) => {
          const optionValue = response.selectedOption?.value || 0;
          const questionScore = response.question?.score || 0;
          return total + optionValue * questionScore;
        }, 0) || 0;
      return {
        id: submission.id,
        date: submission.startAt.toLocaleString('th-TH', {
          timeZone: 'Asia/Bangkok',
          year: 'numeric',
          month: 'short',
          day: 'numeric',
          hour: '2-digit',
          minute: '2-digit',
          hour12: false
        }).replace(/(\d{4})/, (year) => (parseInt(year)).toString()),
        userName: submission.user?.name || '',
        score,      };
    });

    if (sortBy === 'score') {
      formattedParticipants = formattedParticipants.sort((a, b) => {
        return order?.toUpperCase() === 'DESC' 
          ? b.score - a.score 
          : a.score - b.score;
      });
    }

    return {
      data: formattedParticipants,
      total,
      curPage: Number(page),
      hasPrev: Number(page) > 1,
      hasNext: skip + Number(limit) < total,
    };
  }

  async getOneParticipant(
    submissionId: number,
    query: DataParams,
    isTextField?: boolean,
  ) {
    const { page = 1, limit = 5 } = query;
    const skip = (Number(page) - 1) * Number(limit);

    // 🚀 Step 1: Get submission with basic info only
    const submission = await this.submissionRepository
      .createQueryBuilder('submission')
      .leftJoinAndSelect('submission.user', 'user')
      .leftJoinAndSelect('submission.assessment', 'assessment')
      .where('submission.id = :submissionId', { submissionId })
      .getOne();

    if (!submission) {
      return null;
    }

    // 🚀 Step 2: Get itemBlocks with pagination - ใช้ TypeORM QueryBuilder ที่ถูกต้อง
    const itemBlocksQueryBuilder = this.submissionRepository
      .createQueryBuilder('submission')
      .leftJoin('submission.assessment', 'assessment')
      .leftJoin('assessment.itemBlocks', 'itemBlock')
      .where('submission.id = :submissionId', { submissionId })
      .andWhere('itemBlock.type != :headerType', { headerType: 'HEADER' });

    // Apply TEXTFIELD filter if isTextField is true
    if (isTextField) {
      itemBlocksQueryBuilder.andWhere('itemBlock.type = :textfieldType', {
        textfieldType: 'TEXTFIELD',
      });
    }

    const itemBlocksQuery = await itemBlocksQueryBuilder
      .select('itemBlock.id', 'itemBlockId')
      .orderBy('itemBlock.sequence', 'ASC')
      .offset(skip)
      .limit(Number(limit))
      .getRawMany();

    const itemBlockIds = itemBlocksQuery
      .map((row) => row.itemBlockId)
      .filter((id) => id !== null);

    // Now get the full itemBlocks data for the paginated IDs
    const itemBlocks =
      itemBlockIds.length > 0
        ? await this.submissionRepository.manager
            .getRepository('ItemBlock')
            .createQueryBuilder('itemBlock')
            .leftJoinAndSelect('itemBlock.questions', 'question')
            .leftJoinAndSelect('itemBlock.headerBody', 'headerBody')
            .leftJoinAndSelect('itemBlock.imageBody', 'imageBody')
            .leftJoinAndSelect(
              'itemBlock.options',
              'option',
              isTextField ? '1 = 0' : undefined,
            )
            .whereInIds(itemBlockIds)
            .orderBy('itemBlock.sequence', 'ASC')
            .addOrderBy('question.sequence', 'ASC')
            .addOrderBy('option.sequence', 'ASC')
            .getMany()
        : [];

    // 🚀 Step 3: Get total count for pagination - กรอง HEADER ออกด้วย
    const totalItemBlocksQueryBuilder = this.submissionRepository
      .createQueryBuilder('submission')
      .leftJoin('submission.assessment', 'assessment')
      .leftJoin('assessment.itemBlocks', 'itemBlock')
      .where('submission.id = :submissionId', { submissionId })
      .andWhere('itemBlock.type != :headerType', { headerType: 'HEADER' });

    // Apply TEXTFIELD filter if isTextField is true for count
    if (isTextField) {
      totalItemBlocksQueryBuilder.andWhere('itemBlock.type = :textfieldType', {
        textfieldType: 'TEXTFIELD',
      });
    }

    const totalItemBlocks = await totalItemBlocksQueryBuilder
      .select('COUNT(DISTINCT itemBlock.id)', 'count')
      .getRawOne();

    const total = parseInt(totalItemBlocks?.count || '0');

    // 🚀 Step 4: Get user responses for this submission (optimized with specific fields)
    const userResponses = await this.submissionRepository
      .createQueryBuilder('submission')
      .leftJoinAndSelect('submission.responses', 'response')
      .leftJoinAndSelect('response.selectedOption', 'selectedOption')
      .leftJoinAndSelect('response.question', 'responseQuestion')
      .where('submission.id = :submissionId', { submissionId })
      .select([
        'submission.id',
        'response.id',
        'response.questionId',
        'response.selectedOptionId',
        'selectedOption.id',
        'selectedOption.optionText',
        'selectedOption.value',
        'responseQuestion.id',
        'responseQuestion.score',
      ])
      .getOne();

    let totalScore = 0;
    const responses = userResponses?.responses || [];

    // Calculate total score from user responses with special handling for TEXTFIELD
    responses.forEach((response) => {
      const optionValue = response.selectedOption?.value || 0;
      const questionScore = response.question?.score || 0;

      // Get the question's itemBlock to check type
      const questionId = response.question?.id;
      const questionItemBlock = itemBlocks.find((itemBlock) =>
        itemBlock.questions?.some((q) => q.id === questionId),
      );

      if (questionItemBlock?.type === 'TEXTFIELD') {
        // For TEXTFIELD: add raw score from option.value (custom score set by admin)
        totalScore += optionValue;
      } else {
        // For other types: use traditional calculation (correct/incorrect * question score)
        totalScore += optionValue * questionScore;
      }
    });

    // Process itemBlocks with user responses
    const processedItemBlocks = itemBlocks.map((itemBlock) => {
      const blockResponses = responses.filter(
        (response) =>
          response.question &&
          itemBlock.questions?.some((q) => q.id === response.question.id),
      );

      // Process questions in this itemBlock
      const processedQuestions = (itemBlock.questions || []).map((question) => {
        const questionResponse = blockResponses.find(
          (r) => r.question?.id === question.id,
        );
        const selectedOption = questionResponse?.selectedOption;

        return {
          ...question,
          userResponse: questionResponse
            ? {
                id: questionResponse.id,
                selectedOptionId: selectedOption?.id,
                selectedOptionText: selectedOption?.optionText,
                textAnswer:
                  itemBlock.type === 'TEXTFIELD'
                    ? selectedOption?.optionText
                    : undefined,
                isCorrect: selectedOption ? selectedOption.value > 0 : false,
                score: selectedOption
                  ? itemBlock.type === 'TEXTFIELD'
                    ? selectedOption.value // For TEXTFIELD: use raw score (custom score)
                    : selectedOption.value * question.score // For others: multiply by question max score
                  : 0,
              }
            : null,
        };
      });

      // Process options with selection status (skip for textfield)
      const processedOptions = !isTextField
        ? (itemBlock.options || []).map((option) => {
            const isSelected = blockResponses.some(
              (r) => r.selectedOption?.id === option.id,
            );

            return {
              ...option,
              isSelected,
            };
          })
        : [];

      return {
        ...itemBlock,
        questions: processedQuestions,
        ...(isTextField ? {} : { options: processedOptions }),
      };
    });

    const participantData = {
      submissionId: submission.id,
      assessmentId: submission.assessmentId,
      assessmentName: submission.assessment.name,
      userId: submission.user.id,
      userName: submission.user.name,
      startTime: submission.startAt,
      endTime: submission.endAt,
      totalScore: totalScore,
      maxScore: submission.assessment.totalScore,
      scorePercentage:
        submission.assessment.totalScore > 0
          ? (totalScore / submission.assessment.totalScore) * 100
          : 0,
      itemBlocks: processedItemBlocks,
    };

    return {
      data: [participantData],
      total: total,
      curPage: Number(page),
      hasPrev: Number(page) > 1,
      hasNext: skip + Number(limit) < total,
    };
  }

  async saveTextFieldScore(
    submissionId: number,
    questionId: number,
    score: number,
  ): Promise<{ success: boolean; message: string }> {
    const submission = await this.submissionRepository.findOne({
      where: { id: submissionId },
      relations: [
        'responses',
        'responses.question',
        'responses.selectedOption',
      ],
    });

    if (!submission) {
      throw new NotFoundException(
        `Submission with ID ${submissionId} not found`,
      );
    }

    const existingResponse = submission.responses?.find(
      (response) => response.questionId === questionId,
    );

    if (!existingResponse) {
      throw new NotFoundException(
        `Response for question ${questionId} in submission ${submissionId} not found`,
      );
    }

    const question = await this.submissionRepository.manager
      .getRepository('Question')
      .createQueryBuilder('question')
      .leftJoin('question.itemBlock', 'itemBlock')
      .where('question.id = :questionId', { questionId })
      .andWhere('itemBlock.type = :type', { type: 'TEXTFIELD' })
      .getOne();

    if (!question) {
      throw new NotFoundException(
        `TEXTFIELD question with ID ${questionId} not found`,
      );
    }

    if (existingResponse.selectedOption) {
      await this.submissionRepository.manager
        .getRepository('Option')
        .update(existingResponse.selectedOption.id, {
          value: score,
        });

      return {
        success: true,
        message: `Score ${score} saved successfully for question ${questionId}`,
      };
    } else {
      throw new NotFoundException(
        `Selected option not found for response ${existingResponse.id}`,
      );
    }
  }

  private async getUniqueUsersCount(assessmentId: number): Promise<number> {
    const result = await this.submissionRepository
      .createQueryBuilder('submission')
      .select('COUNT(DISTINCT submission.userId)', 'uniqueUsers')
      .where('submission.assessmentId = :assessmentId', { assessmentId })
      .getRawOne();

    return parseInt(result?.uniqueUsers || '0');
  }

  private async getScoreStatistics(assessmentId: number) {
    const result = await this.submissionRepository
      .createQueryBuilder()
      .select([
        'MAX(total_score) as highestScore',
        'MIN(total_score) as lowestScore',
      ])
      .from((subQuery) => {
        return subQuery
          .select([
            'submission.id',
            'SUM(COALESCE(option.value, 0) * COALESCE(question.score, 0)) as total_score',
          ])
          .from(Submission, 'submission')
          .leftJoin('submission.responses', 'response')
          .leftJoin('response.selectedOption', 'option')
          .leftJoin('response.question', 'question')
          .where('submission.assessmentId = :assessmentId', { assessmentId })
          .groupBy('submission.id');
      }, 'submission_scores')
      .getRawOne();

    return {
      highestScore: parseFloat(result?.highestScore || '0'),
      lowestScore: parseFloat(result?.lowestScore || '0'),
    };
  }

  private createEmptyChart(): ChartData {
    return {
      labels: ['No Data'],
      datasets: [{ data: [1], backgroundColor: ['#e0e0e0'] }],
    };
  }

  private async getScoreDistribution(assessmentId: number) {
    return this.submissionRepository
      .createQueryBuilder()
      .select([
        `CASE 
          WHEN score_percentage <= 20 THEN '0-20'
          WHEN score_percentage <= 40 THEN '21-40'
          WHEN score_percentage <= 60 THEN '41-60'
          WHEN score_percentage <= 80 THEN '61-80'
          ELSE '81-100'
        END as scoreRange`,
        'COUNT(*) as count',
      ])
      .from((subQuery) => {
        return subQuery
          .select([
            'submission.id',
            `CASE 
              WHEN SUM(COALESCE(question.score, 0)) > 0 
              THEN (SUM(COALESCE(option.value, 0) * COALESCE(question.score, 0)) * 100.0 / SUM(COALESCE(question.score, 0)))
              ELSE 0
            END as score_percentage`,
          ])
          .from(Submission, 'submission')
          .leftJoin('submission.responses', 'response')
          .leftJoin('response.selectedOption', 'option')
          .leftJoin('response.question', 'question')
          .where('submission.assessmentId = :assessmentId', { assessmentId })
          .groupBy('submission.id');
      }, 'scores')
      .groupBy(
        `CASE 
        WHEN score_percentage <= 20 THEN '0-20'
        WHEN score_percentage <= 40 THEN '21-40'
        WHEN score_percentage <= 60 THEN '41-60'
        WHEN score_percentage <= 80 THEN '61-80'
        ELSE '81-100'
      END`,
      )
      .getRawMany();
  }

  private formatScoreChart(scoreDistribution: any[]): ChartData {
    const scoreRanges = {
      '0-20': { count: 0, color: '#FF6384' },
      '21-40': { count: 0, color: '#FFCE56' },
      '41-60': { count: 0, color: '#36A2EB' },
      '61-80': { count: 0, color: '#4BC0C0' },
      '81-100': { count: 0, color: '#9966FF' },
    };

    scoreDistribution.forEach((result) => {
      const range = result.scoreRange;
      if (scoreRanges[range]) {
        scoreRanges[range].count = parseInt(result.count);
      }
    });

    return {
      labels: Object.keys(scoreRanges),
      datasets: [
        {
          data: Object.values(scoreRanges).map((range) => range.count),
          backgroundColor: Object.values(scoreRanges).map(
            (range) => range.color,
          ),
        },
      ],
    };
  }

  private async getQuestionsForAssessment(
    assessmentId: number,
    query: DataParams,
  ): Promise<DataResponse<any>> {
    const { page = 1, limit = 10, sortBy, order, search } = query;
    const skip = (Number(page) - 1) * Number(limit);

    const whereCondition: any = {
      itemBlock: { assessment: { id: assessmentId } },
    };

    if (search) {
      whereCondition.questionText = Like(`%${search}%`);
    }

    const relations = ['itemBlock', 'itemBlock.assessment'];

    const orderCondition: any = {};
    if (sortBy === 'orderInQuiz') {
      orderCondition.itemBlock = { sequence: (order || 'ASC').toUpperCase() };
    } else if (sortBy) {
      orderCondition[sortBy] = (order || 'ASC').toUpperCase();
    } else {
      orderCondition.itemBlock = { sequence: 'ASC' };
    }

    const [questions, total] = await this.questionRepository.findAndCount({
      where: whereCondition,
      relations,
      order: orderCondition,
      take: Number(limit),
      skip,
    });

    const formattedQuestions = questions.map((question) => ({
      question_id: question.id,
      order_in_quiz: question.itemBlock.sequence,
      question_text: question.questionText,
      question_type: question.itemBlock.type,
    }));

    return {
      data: formattedQuestions,
      total,
      curPage: Number(page),
      hasPrev: Number(page) > 1,
      hasNext: skip + Number(limit) < total,
    };
  }

  private async buildQuestionResponse(
    question: any,
  ): Promise<QuestionResponseData> {
    const options = await this.getQuestionOptions(question.question_id);
    const chartData = this.createQuestionChart(options);

    return {
      questionId: question.question_id,
      orderInQuiz: question.order_in_quiz,
      questionText: question.question_text,
      questionType: question.question_type,
      options,
      chartData,
    };
  }

  private async getQuestionOptions(
    questionId: number,
  ): Promise<OptionResponseData[]> {
    const optionsData = await this.questionRepository
      .createQueryBuilder('question')
      .leftJoin('question.itemBlock', 'itemBlock')
      .leftJoin('itemBlock.options', 'option')
      .leftJoin(
        'option.responses',
        'response',
        'response.questionId = :questionId',
        { questionId },
      )
      .select([
        'option.id as option_id',
        'option.sequence as order_in_question',
        'option.optionText as option_text',
        'COUNT(response.id) as selection_count',
        'option.value as option_value',
      ])
      .where('question.id = :questionId', { questionId })
      .groupBy('option.id, option.sequence, option.optionText, option.value')
      .orderBy('option.sequence', 'ASC')
      .getRawMany();

    return optionsData.map((result) => ({
      optionId: parseInt(result.option_id),
      orderInQuestion: parseInt(result.order_in_question),
      optionText: result.option_text,
      selectionCount: parseInt(result.selection_count || '0'),
      isCorrectAnswer: parseInt(result.option_value) === 1,
    }));
  }

  private createQuestionChart(options: OptionResponseData[]): ChartData {
    const colors = [
      '#FF6384',
      '#36A2EB',
      '#FFCE56',
      '#4BC0C0',
      '#9966FF',
      '#FF9F40',
    ];

    return {
      labels: options.map((option) => option.optionText),
      datasets: [
        {
          data: options.map((option) => option.selectionCount),
          backgroundColor: options.map(
            (_, index) => colors[index % colors.length],
          ),
        },
      ],
    };
  }
}
