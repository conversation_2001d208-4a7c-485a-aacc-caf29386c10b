import {
  Injectable,
  NotFoundException,
  ConflictException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { In, Like, Repository } from 'typeorm';
import { Role } from './entities/role.entity';
import { CreateRoleDto } from './dto/create-role.dto';
import { UpdateRoleDto } from './dto/update-role.dto';
import { Permission } from '../permissions/entities/permission.entity';
import type { DataParams, DataResponse } from 'src/types/params';

@Injectable()
export class RolesService {
  constructor(
    @InjectRepository(Role)
    private readonly roleRepository: Repository<Role>,
    @InjectRepository(Permission)
    private readonly permissionRepository: Repository<Permission>,
  ) {}

  async create(createRoleDto: CreateRoleDto): Promise<Role> {
    const { name, description } = createRoleDto;

    if (!description || description.trim() === '') {
      throw new ConflictException('Description must not be empty or null.');
    }

    const existingRole = await this.roleRepository.findOne({ where: { name } });
    if (existingRole) {
      throw new ConflictException(`Role with name "${name}" already exists.`);
    }

    const newRole = this.roleRepository.create({ name, description });
    const defaultPermissions = await this.permissionRepository.find({
      where: { isDefault: true },
    });

    if (defaultPermissions.length > 0) {
      newRole.permissions = defaultPermissions;
    }

    return this.roleRepository.save(newRole);
  }

  async addPermissionsToRole(roleId: number, permissionIds: number[]) {
    const role = await this.roleRepository.findOne({
      where: { id: roleId },
      relations: ['permissions'],
    });
    if (!role) throw new NotFoundException('Role not found');
    const permissions = await this.permissionRepository.find({
      where: { id: In(permissionIds) },
    });
    if (permissions.length === 0) throw new NotFoundException('Permissions not found');
    const newPermissions = permissions.filter(
      (p) => !role.permissions.some((rp) => rp.id === p.id),
    );
    role.permissions.push(...newPermissions);
    return this.roleRepository.save(role);
  }

  async findAll(pag: DataParams): Promise<DataResponse<Role>> {
    const [roles, total] = await this.roleRepository.findAndCount({
      where: {
        name: pag.search ? Like(`%${pag.search}%`) : undefined,
      },
      relations: ['permissions'],
      order: {
        id: pag.order || 'ASC',
      },
      skip: (pag.page - 1) * pag.limit,
      take: pag.limit,
      cache: true, // Optional: Enable caching for performance
    });
    return {
      data: roles,
      total,
      curPage: pag.page,
      hasNext: total > pag.page * pag.limit,
      hasPrev: pag.page > 1,
    };
  }

  async findOne(id: number): Promise<Role> {
    const role = await this.roleRepository
      .createQueryBuilder('role')
      .leftJoinAndSelect(
        'role.permissions',
        'permission',
        'permission.status = :status',
        { status: true },
      )
      .where('role.id = :id', { id })
      .getOne();

    if (!role) {
      throw new NotFoundException(`Role with ID "${id}" not found`);
    }
    return role;
  }

  async update(id: number, updateRoleDto: UpdateRoleDto): Promise<Role> {
    const role = await this.findOne(id);
    if (updateRoleDto.name && updateRoleDto.name !== role.name) {
      const existingRole = await this.roleRepository.findOneBy({ name: updateRoleDto.name });
      if (existingRole && existingRole.id !== id) {
        throw new ConflictException(`Role with name "${updateRoleDto.name}" already exists.`);
      }
      role.name = updateRoleDto.name;
    }
    if (
      updateRoleDto.description === undefined ||
      updateRoleDto.description.trim() === ''
    ) {
      throw new ConflictException('Description must not be empty.');
    }
    role.description = updateRoleDto.description;
    return this.roleRepository.save(role);
  }

  async remove(id: number): Promise<void> {
    const role = await this.findOne(id);
    await this.roleRepository.remove(role);
  }

  async removePermissionsFromRole(roleId: number, permissionIds: number[]) {
    const role = await this.roleRepository.findOne({
      where: { id: roleId },
      relations: ['permissions'],
    });
    if (!role) throw new NotFoundException('Role not found');
    const permissionsToRemove = await this.permissionRepository.find({ 
      where: { id: In(permissionIds) },
    });
    if (permissionsToRemove.length === 0) throw new NotFoundException('Permissions not found');
    role.permissions = role.permissions.filter(
      (p) => !permissionIds.includes(p.id),
    );

    return this.roleRepository.save(role);
  }

}
