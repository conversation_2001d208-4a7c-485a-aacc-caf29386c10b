import type { RouteRecordRaw } from 'vue-router';

const quizRoutes: RouteRecordRaw[] = [
  {
    path: '/quiz',
    name: 'quiz',
    component: () => import('../../layouts/AsmLayout.vue'),
    beforeEnter: (to, from, next) => {
      if (to.path === '/quiz') {
        next({ name: 'quiz-management' });
      } else {
        next();
      }
    },
    children: [
      {
        path: 'management',
        name: 'quiz-management',
        component: () => import('../../pages/quiz/QuizManagementPage.vue'),
        meta: {},
      },
      {
        path: 'user',
        name: 'quiz-user',
        component: () => import('../../pages/UserAssessmentPage.vue'),
        props: { type: 'quiz' },
      },
      {
        path: ':id(\\d+)/edit',
        name: 'quiz-edit',
        component: () => import('../../pages/quiz/QuizEditorPage.vue'),
        props: true,
      },
      {
        path: ':linkUrl/preview',
        name: 'quiz-preview',
        component: () => import('../../pages/quiz/QuizPageId.vue'),
        props: true,
      },
      {
        path: 'do/:linkUrl',
        name: 'quiz-do',
        component: () => import('../../pages/quiz/QuizPageId.vue'),
      },
      {
        path: '/participant/:participantId',
        name: 'ParticipantDetails',
        component: () => import('../../components/quiz/ParticipantDetailsPage.vue'),
        props: true,
        meta: {
          requiresAuth: true,
          title: 'รายละเอียดผู้เข้าสอบ',
        },
      },
    ],
  },
];

export default quizRoutes;
