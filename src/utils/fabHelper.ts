import { computed, nextTick } from 'vue';
import type { ItemBlock } from 'src/types/models';

export interface FabPositionState {
  positionLock: boolean;
  pendingPosition: number | null;
  creationInProgress: boolean;
  targetBlockId: number | null;
}

export interface TimeoutManager {
  fabPositionTimeout: NodeJS.Timeout | null;
  lockReleaseTimeout: NodeJS.Timeout | null;
  clearFabTimeout(): void;
  clearLockTimeout(): void;
  clearAll(): void;
}

export interface FabPositionManager {
  setFabPosition(blockId: number, immediate?: boolean): void;
  createFabProtection(blockId: number, duration?: number): void;
  scrollToTarget(): void;
  setFabAndScroll(id: number): Promise<void>;
}

/**
 * Creates a timeout manager for handling FAB positioning timeouts
 */
export function createTimeoutManager(): TimeoutManager {
  const manager = {
    fabPositionTimeout: null as NodeJS.Timeout | null,
    lockReleaseTimeout: null as NodeJS.Timeout | null,

    clearFabTimeout() {
      if (this.fabPositionTimeout) {
        clearTimeout(this.fabPositionTimeout);
        this.fabPositionTimeout = null;
      }
    },

    clearLockTimeout() {
      if (this.lockReleaseTimeout) {
        clearTimeout(this.lockReleaseTimeout);
        this.lockReleaseTimeout = null;
      }
    },

    clearAll() {
      this.clearFabTimeout();
      this.clearLockTimeout();
    },
  };

  return manager;
}

/**
 * Creates a FAB position manager with improved performance and state management
 */
export function createFabPositionManager(
  fabState: { value: FabPositionState },
  selectedBlockId: { value: string | undefined },
  isCreatingBlock: { value: boolean },
  timeoutManager: TimeoutManager,
  getBlockRef: (id: number) => Element | null,
): FabPositionManager {
  const fabPositionLock = computed({
    get: () => fabState.value.positionLock,
    set: (value: boolean) => {
      fabState.value.positionLock = value;
    },
  });

  const pendingFabPosition = computed({
    get: () => fabState.value.pendingPosition,
    set: (value: number | null) => {
      fabState.value.pendingPosition = value;
    },
  });

  const blockCreationInProgress = computed({
    get: () => fabState.value.creationInProgress,
    set: (value: boolean) => {
      fabState.value.creationInProgress = value;
    },
  });

  const targetBlockId = computed({
    get: () => fabState.value.targetBlockId,
    set: (value: number | null) => {
      fabState.value.targetBlockId = value;
    },
  });

  const setFabPosition = (blockId: number, immediate = false) => {
    // Early return for block creation priority
    if (isCreatingBlock.value && !immediate) {
      pendingFabPosition.value = blockId;
      return;
    }

    // Clear existing timeouts
    timeoutManager.clearFabTimeout();

    if (immediate) {
      // Immediate positioning for critical operations
      fabPositionLock.value = true;
      selectedBlockId.value = `block-${blockId}`;

      // Clear any existing lock release timeout
      timeoutManager.clearLockTimeout();

      // Set new lock release timeout
      timeoutManager.lockReleaseTimeout = setTimeout(() => {
        fabPositionLock.value = false;
        // Apply any pending position change
        const pending = pendingFabPosition.value;
        if (pending && pending !== blockId) {
          setFabPosition(pending, false);
          pendingFabPosition.value = null;
        }
      }, 200);
    } else {
      // Debounced positioning for regular interactions
      timeoutManager.fabPositionTimeout = setTimeout(() => {
        if (!fabPositionLock.value) {
          selectedBlockId.value = `block-${blockId}`;
        } else {
          // Store as pending if locked
          pendingFabPosition.value = blockId;
        }
      }, 50);
    }
  };

  const createFabProtection = (blockId: number, duration = 200) => {
    blockCreationInProgress.value = true;
    targetBlockId.value = blockId;
    fabPositionLock.value = true;
    selectedBlockId.value = `block-${blockId}`;

    // Clear any existing protection timeout
    timeoutManager.clearLockTimeout();

    // Set new protection timeout
    timeoutManager.lockReleaseTimeout = setTimeout(() => {
      fabPositionLock.value = false;
      blockCreationInProgress.value = false;
      targetBlockId.value = null;
      // Ensure FAB stays on the updated block
      selectedBlockId.value = `block-${blockId}`;
    }, duration);
  };

  const scrollToTarget = () => {
    if (!selectedBlockId.value) return;
    const id = Number(selectedBlockId.value.split('-')[1]);
    const el = getBlockRef(id);
    if (el && 'scrollIntoView' in el) {
      (el as HTMLElement).scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
  };

  const setFabAndScroll = async (id: number) => {
    // Use immediate positioning to prevent conflicts during block creation
    setFabPosition(id, true);

    // Wait for DOM updates
    await nextTick();
    await nextTick();

    // Scroll to the target block
    scrollToTarget();
  };

  return {
    setFabPosition,
    createFabProtection,
    scrollToTarget,
    setFabAndScroll,
  };
}

/**
 * Creates a sequence updater for managing block sequences
 */
export function createSequenceUpdater() {
  const updateBlockSequences = (blocks: ItemBlock[]) => {
    blocks.forEach((block, index) => {
      block.sequence = index + 1;
    });
  };

  return {
    updateBlockSequences,
  };
}

/**
 * Creates an assessment synchronizer for keeping assessment and blocks in sync
 */
export function createAssessmentSynchronizer() {
  const syncBlocksWithAssessment = async (
    blocks: { value: ItemBlock[] },
    currentAssessment: { value: { itemBlocks?: ItemBlock[] } | null },
    forceRefreshBlocks: () => Promise<void>,
  ) => {
    if (currentAssessment.value && currentAssessment.value.itemBlocks) {
      // Sync blocks with currentAssessment
      blocks.value = [...currentAssessment.value.itemBlocks];
      await forceRefreshBlocks();
    }
  };

  const updateAssessmentBlocks = (
    currentAssessment: { value: { itemBlocks?: ItemBlock[] } | null },
    blocks: ItemBlock[],
  ) => {
    if (currentAssessment.value) {
      currentAssessment.value.itemBlocks = [...blocks];
    }
  };

  return {
    syncBlocksWithAssessment,
    updateAssessmentBlocks,
  };
}

/**
 * Creates a force update utility with cleaner timeout management
 */
export function createForceUpdateUtility() {
  const executeForceUpdate = async (forceUpdateTrigger: { value: number }, iterations = 5) => {
    for (let i = 0; i < iterations; i++) {
      forceUpdateTrigger.value++;
      await nextTick();
    }

    // Additional delayed updates for complex DOM changes
    const delayedUpdate = (delay: number) =>
      new Promise<void>((resolve) => {
        setTimeout(() => {
          forceUpdateTrigger.value++;
          resolve();
        }, delay);
      });

    await delayedUpdate(200);
    await nextTick();
    await delayedUpdate(100);
    await nextTick();
    await delayedUpdate(100);
  };

  return {
    executeForceUpdate,
  };
}
