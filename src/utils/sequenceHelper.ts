import type { ItemBlock } from 'src/types/models';

/**
 * Updates block sequences to maintain proper order
 */
export function updateBlockSequences(blocks: ItemBlock[]): void {
  blocks.forEach((block, index) => {
    block.sequence = index + 1;
  });
}

/**
 * Updates section numbers after header block deletion
 */
export function updateSectionNumbers(
  blocks: ItemBlock[],
  deletedSectionNumber: number,
): {
  updatedBlocks: ItemBlock[];
  sectionMap: Map<number, number>;
} {
  // Remove blocks from deleted section
  const remainingBlocks = blocks.filter((block) => block.section !== deletedSectionNumber);

  // Get header blocks and sort by section
  const headerBlocks = remainingBlocks
    .filter((block) => block.type === 'HEADER')
    .sort((a, b) => a.section - b.section);

  // Create section mapping
  const sectionMap = new Map<number, number>();
  headerBlocks.forEach((header, idx) => {
    const oldSection = header.section;
    const newSection = idx + 1;
    sectionMap.set(oldSection, newSection);
    header.section = newSection;
  });

  // Update all blocks with new section numbers
  const updatedBlocks: ItemBlock[] = [];
  remainingBlocks.forEach((block) => {
    if (sectionMap.has(block.section)) {
      const oldSection = block.section;
      const newSection = sectionMap.get(block.section)!;
      if (oldSection !== newSection) {
        block.section = newSection;
        updatedBlocks.push(block);
      }
    }
  });

  return {
    updatedBlocks,
    sectionMap,
  };
}

/**
 * Calculates the next sequence number for a new block
 */
export function calculateNextSequence(_blocks: ItemBlock[], insertIndex: number): number {
  return insertIndex + 2;
}

/**
 * Updates sequences for blocks after insertion
 */
export function updateSequencesAfterInsertion(
  blocks: ItemBlock[],
  insertIndex: number,
  newBlock: ItemBlock,
): void {
  // Insert the new block
  blocks.splice(insertIndex + 1, 0, newBlock);

  // Update all sequences
  updateBlockSequences(blocks);
}

/**
 * Updates sequences for blocks after deletion
 */
export function updateSequencesAfterDeletion(blocks: ItemBlock[], deletedIndex: number): void {
  // Remove the block
  blocks.splice(deletedIndex, 1);

  // Update all sequences
  updateBlockSequences(blocks);
}

/**
 * Finds the highest section number in blocks
 */
export function findMaxSectionNumber(blocks: ItemBlock[]): number {
  return blocks.reduce((max, block) => Math.max(max, block.section || 1), 1);
}

/**
 * Gets blocks that belong to a specific section
 */
export function getBlocksBySection(blocks: ItemBlock[], sectionNumber: number): ItemBlock[] {
  return blocks.filter((block) => block.section === sectionNumber);
}

/**
 * Updates assessment blocks with new order and sequences
 */
export function updateAssessmentBlockOrder(
  assessmentBlocks: ItemBlock[],
  newBlock: ItemBlock,
  insertIndex: number,
): ItemBlock[] {
  // Insert at correct position
  const newAssessmentBlocks = [
    ...assessmentBlocks.slice(0, insertIndex + 1),
    newBlock,
    ...assessmentBlocks.slice(insertIndex + 1),
  ];

  // Update sequences for assessment blocks
  updateBlockSequences(newAssessmentBlocks);

  return newAssessmentBlocks;
}

/**
 * Reorders blocks array with new order
 */
export function reorderBlocks(blocks: ItemBlock[], newOrder: ItemBlock[]): void {
  // Update the blocks array with the new order
  blocks.splice(0, blocks.length, ...newOrder);

  // Update sequence numbers to match the new order
  updateBlockSequences(blocks);
}

/**
 * Validates sequence integrity
 */
export function validateSequenceIntegrity(blocks: ItemBlock[]): {
  valid: boolean;
  issues: string[];
} {
  const issues: string[] = [];
  const sequences = blocks.map((block) => block.sequence).sort((a, b) => a - b);

  for (let i = 0; i < sequences.length; i++) {
    if (sequences[i] !== i + 1) {
      issues.push(`Expected sequence ${i + 1}, found ${sequences[i]}`);
    }
  }

  return {
    valid: issues.length === 0,
    issues,
  };
}

/**
 * Fixes sequence numbers if they are out of order
 */
export function fixSequenceOrder(blocks: ItemBlock[]): {
  fixed: boolean;
  changes: number;
} {
  let changes = 0;

  blocks.forEach((block, index) => {
    const expectedSequence = index + 1;
    if (block.sequence !== expectedSequence) {
      block.sequence = expectedSequence;
      changes++;
    }
  });

  return {
    fixed: changes > 0,
    changes,
  };
}

/**
 * Duplicates a block and inserts it at the correct position with proper sequence
 */
export function insertDuplicatedBlock(
  blocks: ItemBlock[],
  sourceIndex: number,
  duplicatedBlock: ItemBlock,
): void {
  const insertIndex = sourceIndex + 1;

  // Validate source index and block
  const sourceBlock = blocks[sourceIndex];
  if (!sourceBlock) {
    throw new Error(`Source block at index ${sourceIndex} is undefined`);
  }

  // Set the sequence for the duplicated block
  duplicatedBlock.sequence = sourceBlock.sequence + 1;

  // Insert the duplicated block
  blocks.splice(insertIndex, 0, duplicatedBlock);

  // Update sequences for all blocks after insertion
  for (let i = insertIndex + 1; i < blocks.length; i++) {
    const block = blocks[i];
    if (block) {
      block.sequence = i + 1;
    }
  }
}
